'use strict';

exports.main = async (event, context) => {
  console.log('重置管理员密码...');
  
  try {
    const db = uniCloud.database();
    
    // 查找admin用户
    const adminUsers = await db.collection('uni-id-users').where({
      username: 'admin'
    }).get();
    
    if (adminUsers.data.length === 0) {
      // 如果没有admin用户，创建一个
      const result = await db.collection('uni-id-users').add({
        username: 'admin',
        password: '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
        nickname: '系统管理员',
        role: ['admin'],
        status: 0,
        register_date: new Date()
      });
      
      return {
        code: 0,
        message: '管理员账号创建成功',
        data: {
          username: 'admin',
          password: 'admin123456',
          userId: result.id
        }
      };
    } else {
      // 如果有admin用户，更新密码
      const adminId = adminUsers.data[0]._id;
      await db.collection('uni-id-users').doc(adminId).update({
        password: '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
        status: 0,
        role: ['admin']
      });
      
      return {
        code: 0,
        message: '管理员密码重置成功',
        data: {
          username: 'admin',
          password: 'admin123456',
          note: '密码已重置为admin123456'
        }
      };
    }
    
  } catch (error) {
    console.error('重置密码失败:', error);
    return {
      code: -1,
      message: '重置失败',
      error: error.message
    };
  }
};
