{"bsonType": "object", "description": "消息通知表", "required": ["user_id", "title", "content", "type"], "properties": {"_id": {"description": "消息ID"}, "user_id": {"bsonType": "string", "description": "接收用户ID", "label": "接收用户", "foreignKey": "uni-id-users._id"}, "title": {"bsonType": "string", "description": "消息标题", "label": "标题", "maxLength": 100}, "content": {"bsonType": "string", "description": "消息内容", "label": "内容", "maxLength": 1000}, "type": {"bsonType": "string", "description": "消息类型", "label": "类型", "enum": ["system", "appointment", "house", "favorite"], "enumDesc": ["系统消息", "预约消息", "房源消息", "收藏消息"]}, "related_id": {"bsonType": "string", "description": "关联ID（房源ID、预约ID等）", "label": "关联ID"}, "is_read": {"bsonType": "bool", "description": "是否已读", "label": "已读", "default": false}, "create_time": {"bsonType": "timestamp", "description": "创建时间", "label": "创建时间", "forceDefaultValue": {"$env": "now"}}, "read_time": {"bsonType": "timestamp", "description": "阅读时间", "label": "阅读时间"}}}