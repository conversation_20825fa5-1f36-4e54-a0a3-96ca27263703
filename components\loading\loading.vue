<template>
  <view class="loading-container" v-if="show">
    <view class="loading-content">
      <view class="loading-spinner" :class="{ 'loading-animate': animate }">
        <view class="spinner-dot" v-for="i in 8" :key="i" :style="{ 'animation-delay': (i - 1) * 0.1 + 's' }"></view>
      </view>
      <text class="loading-text" v-if="text">{{ text }}</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'Loading',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    text: {
      type: String,
      default: '加载中...'
    },
    animate: {
      type: Boolean,
      default: true
    }
  }
}
</script>

<style scoped>
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  background: #fff;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 200rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  position: relative;
  margin-bottom: 30rpx;
}

.spinner-dot {
  position: absolute;
  width: 8rpx;
  height: 8rpx;
  background: #007aff;
  border-radius: 50%;
  opacity: 0;
}

.loading-animate .spinner-dot {
  animation: spinner-fade 1.2s linear infinite;
}

.spinner-dot:nth-child(1) {
  top: 0;
  left: 50%;
  transform: translateX(-50%);
}

.spinner-dot:nth-child(2) {
  top: 15%;
  right: 15%;
}

.spinner-dot:nth-child(3) {
  top: 50%;
  right: 0;
  transform: translateY(-50%);
}

.spinner-dot:nth-child(4) {
  bottom: 15%;
  right: 15%;
}

.spinner-dot:nth-child(5) {
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
}

.spinner-dot:nth-child(6) {
  bottom: 15%;
  left: 15%;
}

.spinner-dot:nth-child(7) {
  top: 50%;
  left: 0;
  transform: translateY(-50%);
}

.spinner-dot:nth-child(8) {
  top: 15%;
  left: 15%;
}

@keyframes spinner-fade {
  0%, 39%, 100% {
    opacity: 0;
  }
  40% {
    opacity: 1;
  }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
  text-align: center;
}
</style>
