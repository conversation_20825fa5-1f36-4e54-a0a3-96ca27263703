<template>
  <view class="favorites-container">
    <!-- 房源列表 -->
    <scroll-view 
      class="house-list" 
      scroll-y 
      @scrolltolower="loadMore"
      refresher-enabled
      @refresherrefresh="onRefresh"
      :refresher-triggered="refreshing"
    >
      <view class="house-item" v-for="item in favoriteList" :key="item.favorite_id">
        <view class="house-image" @click="toHouseDetail(item.house._id)">
          <image 
            :src="item.house.images && item.house.images[0] || '/static/default-house.png'" 
            mode="aspectFill"
          ></image>
          <view class="house-type">{{ getTypeText(item.house.type) }}</view>
        </view>
        <view class="house-info" @click="toHouseDetail(item.house._id)">
          <text class="house-title">{{ item.house.title }}</text>
          <text class="house-desc">{{ item.house.description }}</text>
          <view class="house-tags">
            <text class="tag" v-if="item.house.room_count">{{ item.house.room_count }}室</text>
            <text class="tag" v-if="item.house.hall_count">{{ item.house.hall_count }}厅</text>
            <text class="tag" v-if="item.house.area">{{ item.house.area }}㎡</text>
          </view>
          <view class="house-location">
            <uni-icons type="location" size="12" color="#999"></uni-icons>
            <text class="location-text">{{ item.house.location.district }} {{ item.house.location.address }}</text>
          </view>
          <view class="house-bottom">
            <view class="price-info">
              <text class="price">¥{{ item.house.price }}</text>
              <text class="price-unit">/月</text>
            </view>
            <text class="favorite-time">{{ formatRelativeTime(item.create_date) }}</text>
          </view>
        </view>
        <view class="house-actions">
          <button class="action-btn unfavorite" @click="removeFavorite(item)">
            <uni-icons type="heart-filled" size="20" color="#ff6b6b"></uni-icons>
          </button>
          <button class="action-btn contact" @click="contactLandlord(item.house)">
            <uni-icons type="chatbubble" size="20" color="#007aff"></uni-icons>
          </button>
        </view>
      </view>
      
      <!-- 加载状态 -->
      <view class="load-status" v-if="favoriteList.length > 0">
        <text v-if="loading">加载中...</text>
        <text v-else-if="noMore">没有更多了</text>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" v-if="!loading && favoriteList.length === 0">
        <image src="/static/empty-favorite.png" mode="aspectFit"></image>
        <text class="empty-text">暂无收藏房源</text>
        <text class="empty-tip">去看看有什么好房源吧</text>
        <button class="browse-btn" @click="toBrowse">去逛逛</button>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import request from '@/utils/request.js'
import { formatRelativeTime } from '@/utils/common.js'
import { HOUSE_TYPES } from '@/common/config.js'

export default {
  data() {
    return {
      favoriteList: [],
      loading: false,
      refreshing: false,
      noMore: false,
      page: 1,
      pageSize: 10
    }
  },
  methods: {
    formatRelativeTime,
    
    // 获取房源类型文本
    getTypeText(type) {
      const typeItem = HOUSE_TYPES.find(item => item.value === type)
      return typeItem ? typeItem.label : type
    },
    
    // 加载收藏列表
    async loadFavoriteList(refresh = false) {
      if (this.loading) return
      
      this.loading = true
      
      if (refresh) {
        this.page = 1
        this.noMore = false
      }
      
      try {
        const result = await request.callFunction('favorite-management', {
          action: 'getFavoriteList',
          data: {
            page: this.page,
            pageSize: this.pageSize
          }
        })
        
        if (result.code === 0) {
          const { list, total } = result.data
          
          if (refresh) {
            this.favoriteList = list
          } else {
            this.favoriteList.push(...list)
          }
          
          this.page++
          this.noMore = this.favoriteList.length >= total
        }
      } catch (error) {
        console.error('加载收藏列表失败:', error)
      } finally {
        this.loading = false
        this.refreshing = false
      }
    },
    
    // 加载更多
    loadMore() {
      if (!this.noMore && !this.loading) {
        this.loadFavoriteList()
      }
    },
    
    // 下拉刷新
    onRefresh() {
      this.refreshing = true
      this.loadFavoriteList(true)
    },
    
    // 取消收藏
    async removeFavorite(item) {
      uni.showModal({
        title: '提示',
        content: '确定要取消收藏吗？',
        success: async (res) => {
          if (res.confirm) {
            try {
              const result = await request.callFunction('favorite-management', {
                action: 'removeFavorite',
                data: {
                  house_id: item.house._id
                }
              })
              
              if (result.code === 0) {
                uni.showToast({
                  title: '取消收藏成功',
                  icon: 'success'
                })
                
                // 从列表中移除
                const index = this.favoriteList.findIndex(fav => fav.favorite_id === item.favorite_id)
                if (index > -1) {
                  this.favoriteList.splice(index, 1)
                }
              }
            } catch (error) {
              console.error('取消收藏失败:', error)
            }
          }
        }
      })
    },
    
    // 联系房东
    contactLandlord(house) {
      if (!house.contact) {
        uni.showToast({
          title: '暂无联系方式',
          icon: 'none'
        })
        return
      }
      
      uni.showActionSheet({
        itemList: ['拨打电话', '复制微信号'],
        success: (res) => {
          if (res.tapIndex === 0) {
            // 拨打电话
            uni.makePhoneCall({
              phoneNumber: house.contact.phone
            })
          } else if (res.tapIndex === 1) {
            // 复制微信号
            if (house.contact.wechat) {
              uni.setClipboardData({
                data: house.contact.wechat,
                success: () => {
                  uni.showToast({
                    title: '微信号已复制',
                    icon: 'success'
                  })
                }
              })
            } else {
              uni.showToast({
                title: '暂无微信号',
                icon: 'none'
              })
            }
          }
        }
      })
    },
    
    // 跳转到房源详情
    toHouseDetail(houseId) {
      uni.navigateTo({
        url: `/pages/house/detail?id=${houseId}`
      })
    },
    
    // 跳转到浏览页面
    toBrowse() {
      uni.switchTab({
        url: '/pages/house/list'
      })
    }
  },
  
  onLoad() {
    this.loadFavoriteList(true)
  },
  
  onShow() {
    // 从详情页返回时刷新列表
    this.loadFavoriteList(true)
  },
  
  onPullDownRefresh() {
    this.onRefresh()
    uni.stopPullDownRefresh()
  }
}
</script>

<style scoped>
.favorites-container {
  height: 100vh;
  background: #f8f9fa;
}

.house-list {
  height: 100%;
  padding: 20rpx;
}

.house-item {
  background: #fff;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  display: flex;
}

.house-image {
  position: relative;
  width: 240rpx;
  height: 200rpx;
  flex-shrink: 0;
}

.house-image image {
  width: 100%;
  height: 100%;
}

.house-type {
  position: absolute;
  top: 15rpx;
  left: 15rpx;
  background: rgba(0, 122, 255, 0.9);
  color: #fff;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
}

.house-info {
  flex: 1;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.house-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.house-desc {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.house-tags {
  display: flex;
  gap: 8rpx;
  margin-bottom: 10rpx;
  flex-wrap: wrap;
}

.tag {
  background: #f0f0f0;
  color: #666;
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
  font-size: 22rpx;
}

.house-location {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.location-text {
  font-size: 24rpx;
  color: #999;
  margin-left: 6rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.house-bottom {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.price-info {
  display: flex;
  align-items: baseline;
}

.price {
  font-size: 30rpx;
  font-weight: bold;
  color: #ff6b6b;
}

.price-unit {
  font-size: 20rpx;
  color: #999;
  margin-left: 4rpx;
}

.favorite-time {
  font-size: 22rpx;
  color: #ccc;
}

.house-actions {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 20rpx;
  gap: 20rpx;
  border-left: 1rpx solid #f0f0f0;
}

.action-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn.unfavorite {
  background: #ffebee;
}

.action-btn.contact {
  background: #e3f2fd;
}

.load-status {
  text-align: center;
  padding: 40rpx;
  color: #999;
  font-size: 28rpx;
}

.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
}

.empty-state image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
}

.empty-text {
  display: block;
  font-size: 28rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.empty-tip {
  display: block;
  font-size: 24rpx;
  color: #ccc;
  margin-bottom: 40rpx;
}

.browse-btn {
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 25rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}
</style>
