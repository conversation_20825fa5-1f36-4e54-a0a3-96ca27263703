<template>
  <view class="index-container">
    <!-- 头部搜索栏 -->
    <view class="header" :style="{ paddingTop: (statusBarHeight * 2 + 60) + 'rpx' }">
      <view class="header-content">
        <view class="greeting-section">
          <text class="greeting-text">Hi，找房子吗？</text>
          <text class="greeting-sub">为您推荐优质房源</text>
        </view>
        <view class="location-btn" @click="chooseLocation">
          <uni-icons type="location" size="16" color="#fff"></uni-icons>
          <text class="location-text">{{ currentLocation || '定位' }}</text>
        </view>
      </view>

      <view class="search-section">
        <view class="search-bar" @click="toSearch">
          <uni-icons type="search" size="20" color="#999"></uni-icons>
          <text class="search-placeholder">搜索房源、地址、关键词</text>
          <view class="search-btn">
            <text class="search-btn-text">搜索</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 轮播图 -->
    <view class="banner-section">
      <swiper
        class="banner-swiper"
        indicator-dots
        indicator-color="rgba(255,255,255,0.4)"
        indicator-active-color="#fff"
        autoplay
        circular
        interval="4000"
        duration="500"
      >
        <swiper-item v-for="(banner, index) in banners" :key="index">
          <view class="banner-item" @click="onBannerClick(banner)">
            <image :src="banner.image" mode="aspectFill" class="banner-image"></image>
            <view class="banner-overlay">
              <text class="banner-title">{{ banner.title }}</text>
              <text class="banner-desc">{{ banner.desc || '发现更多优质房源' }}</text>
            </view>
          </view>
        </swiper-item>
      </swiper>
    </view>

    <!-- 快捷入口 -->
    <view class="quick-section">
      <view class="section-header">
        <text class="section-title">快速找房</text>
        <text class="section-subtitle">选择您需要的房源类型</text>
      </view>

      <view class="quick-grid">
        <view class="quick-item" @click="toHouseList('whole')">
          <view class="quick-icon whole">
            <text class="icon-emoji">🏠</text>
          </view>
          <text class="quick-title">整租</text>
          <text class="quick-desc">独立空间</text>
        </view>
        <view class="quick-item" @click="toHouseList('shared')">
          <view class="quick-icon shared">
            <text class="icon-emoji">👥</text>
          </view>
          <text class="quick-title">合租</text>
          <text class="quick-desc">性价比高</text>
        </view>
        <view class="quick-item" @click="toHouseList('single')">
          <view class="quick-icon single">
            <text class="icon-emoji">🛏️</text>
          </view>
          <text class="quick-title">单间</text>
          <text class="quick-desc">经济实惠</text>
        </view>
        <view class="quick-item" @click="toMap">
          <view class="quick-icon map">
            <text class="icon-emoji">🗺️</text>
          </view>
          <text class="quick-title">地图找房</text>
          <text class="quick-desc">位置优先</text>
        </view>
      </view>
    </view>

    <!-- 推荐房源 -->
    <view class="recommend-section">
      <view class="content-header">
        <view class="header-left">
          <text class="content-title">🔥 热门推荐</text>
          <text class="content-subtitle">精选优质房源</text>
        </view>
        <view class="more-btn" @click="toHouseList()">
          <text class="more-text">更多</text>
          <uni-icons type="arrowright" size="14" color="#007aff"></uni-icons>
        </view>
      </view>

      <view v-if="loading" class="loading-container">
        <view class="loading-content">
          <view class="loading-spinner"></view>
          <text class="loading-text">加载中...</text>
        </view>
      </view>

      <scroll-view v-else-if="recommendHouses.length > 0" class="recommend-scroll" scroll-x show-scrollbar="false">
        <view class="recommend-list">
          <view class="recommend-card" v-for="house in recommendHouses" :key="house._id" @click="toHouseDetail(house._id)">
            <view class="card-image">
              <image :src="house.images && house.images[0] || '/static/default-house.png'" mode="aspectFill" class="house-image"></image>
              <view class="image-overlay">
                <view class="house-type-tag">{{ getTypeText(house.type) }}</view>
                <view class="favorite-btn">
                  <uni-icons type="heart" size="16" color="#fff"></uni-icons>
                </view>
              </view>
            </view>
            <view class="card-content">
              <text class="card-title">{{ house.title }}</text>
              <view class="card-tags">
                <text class="tag" v-if="house.room_count">{{ house.room_count }}室</text>
                <text class="tag" v-if="house.hall_count">{{ house.hall_count }}厅</text>
                <text class="tag" v-if="house.area">{{ house.area }}㎡</text>
              </view>
              <view class="card-location">
                <uni-icons type="location" size="12" color="#999"></uni-icons>
                <text class="location-text">{{ house.location.district }}</text>
              </view>
              <view class="card-price">
                <text class="price">¥{{ house.price }}</text>
                <text class="price-unit">/月</text>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>

      <view v-else class="empty-state">
        <view class="empty-icon">🏠</view>
        <text class="empty-title">暂无推荐房源</text>
        <text class="empty-desc">稍后再来看看吧</text>
      </view>
    </view>

    <!-- 最新房源 -->
    <view class="latest-section">
      <view class="content-header">
        <view class="header-left">
          <text class="content-title">✨ 最新发布</text>
          <text class="content-subtitle">新鲜出炉的房源</text>
        </view>
        <view class="more-btn" @click="toHouseList()">
          <text class="more-text">更多</text>
          <uni-icons type="arrowright" size="14" color="#007aff"></uni-icons>
        </view>
      </view>

      <view v-if="loading" class="loading-container">
        <view class="loading-content">
          <view class="loading-spinner"></view>
          <text class="loading-text">加载中...</text>
        </view>
      </view>

      <view v-else-if="latestHouses.length > 0" class="latest-list">
        <view class="latest-card" v-for="house in latestHouses" :key="house._id" @click="toHouseDetail(house._id)">
          <view class="card-image-container">
            <image :src="house.images && house.images[0] || '/static/default-house.png'" mode="aspectFill" class="card-image"></image>
            <view class="image-badges">
              <view class="type-badge">{{ getTypeText(house.type) }}</view>
              <view class="new-badge">NEW</view>
            </view>
          </view>
          <view class="card-info">
            <view class="info-header">
              <text class="info-title">{{ house.title }}</text>
              <view class="favorite-icon">
                <uni-icons type="heart" size="16" color="#ddd"></uni-icons>
              </view>
            </view>
            <text class="info-desc">{{ house.description || '暂无描述' }}</text>
            <view class="info-tags">
              <text class="info-tag" v-if="house.room_count">{{ house.room_count }}室</text>
              <text class="info-tag" v-if="house.hall_count">{{ house.hall_count }}厅</text>
              <text class="info-tag" v-if="house.area">{{ house.area }}㎡</text>
            </view>
            <view class="info-location">
              <uni-icons type="location" size="14" color="#999"></uni-icons>
              <text class="location-detail">{{ house.location.district }} {{ house.location.address }}</text>
            </view>
            <view class="info-bottom">
              <view class="price-section">
                <text class="price-amount">¥{{ house.price }}</text>
                <text class="price-period">/月</text>
              </view>
              <view class="stats-section">
                <view class="stat-item">
                  <uni-icons type="eye" size="12" color="#999"></uni-icons>
                  <text class="stat-text">{{ house.view_count || 0 }}</text>
                </view>
                <view class="stat-item">
                  <uni-icons type="heart" size="12" color="#999"></uni-icons>
                  <text class="stat-text">{{ house.favorite_count || 0 }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <view v-else class="empty-state">
        <view class="empty-icon">📝</view>
        <text class="empty-title">暂无最新房源</text>
        <text class="empty-desc">稍后再来看看吧</text>
      </view>
    </view>

    <!-- 底部间距 -->
    <view class="bottom-spacing"></view>
  </view>
</template>

<script>
import request from '@/utils/request.js'
import { HOUSE_TYPES } from '@/common/config.js'

export default {
  data() {
    return {
      currentLocation: '',
      loading: false,
      statusBarHeight: 0, // 状态栏高度
      banners: [
        {
          image: '/static/banner1.jpg',
          title: '精选优质房源',
          desc: '为您推荐性价比最高的房源',
          url: '/pages/house/list?sort=favorite_count_desc'
        },
        {
          image: '/static/banner2.jpg',
          title: '毕业生租房专区',
          desc: '专为毕业生打造的租房平台',
          url: '/pages/house/list?type=single'
        },
        {
          image: '/static/banner3.jpg',
          title: '安全租房保障',
          desc: '真实房源，安全交易，放心租房',
          url: '/pages/house/list'
        }
      ],
      recommendHouses: [],
      latestHouses: []
    }
  },
  methods: {
    // 获取房源类型文本
    getTypeText(type) {
      const typeItem = HOUSE_TYPES.find(item => item.value === type)
      return typeItem ? typeItem.label : type
    },

    // 加载推荐房源
    async loadRecommendHouses() {
      try {
        console.log('开始加载推荐房源...')
        const result = await request.callFunction('house-management', {
          action: 'getHouseList',
          data: {
            page: 1,
            pageSize: 5,
            sort: 'favorite_count_desc'
          }
        })

        console.log('推荐房源返回结果:', result)
        if (result.code === 0) {
          this.recommendHouses = result.data.list || []
          console.log('推荐房源数据:', this.recommendHouses)
        } else {
          console.error('推荐房源请求失败:', result)
        }
      } catch (error) {
        console.error('加载推荐房源失败:', error)
        uni.showToast({
          title: '加载推荐房源失败',
          icon: 'none'
        })
      }
    },

    // 加载最新房源
    async loadLatestHouses() {
      try {
        console.log('开始加载最新房源...')
        const result = await request.callFunction('house-management', {
          action: 'getHouseList',
          data: {
            page: 1,
            pageSize: 3,
            sort: 'publish_date_desc'
          }
        })

        console.log('最新房源返回结果:', result)
        if (result.code === 0) {
          this.latestHouses = result.data.list || []
          console.log('最新房源数据:', this.latestHouses)
        } else {
          console.error('最新房源请求失败:', result)
        }
      } catch (error) {
        console.error('加载最新房源失败:', error)
        uni.showToast({
          title: '加载最新房源失败',
          icon: 'none'
        })
      }
    },

    // 加载所有数据
    async loadData() {
      this.loading = true
      try {
        await Promise.all([
          this.loadRecommendHouses(),
          this.loadLatestHouses()
        ])
      } finally {
        this.loading = false
      }
    },

    // 跳转到搜索页
    toSearch() {
      uni.navigateTo({
        url: '/pages/search/search'
      })
    },

    // 选择位置
    chooseLocation() {
      uni.chooseLocation({
        success: (res) => {
          this.currentLocation = res.name || res.address
          uni.setStorageSync('userLocation', {
            name: this.currentLocation,
            latitude: res.latitude,
            longitude: res.longitude
          })
        },
        fail: (error) => {
          console.error('选择位置失败:', error)
        }
      })
    },

    // 轮播图点击
    onBannerClick(banner) {
      if (banner.url) {
        // 检查是否是 tabBar 页面
        const tabBarPages = [
          '/pages/index/index',
          '/pages/house/list',
          '/pages/search/search',
          '/pages/user/profile'
        ]

        // 提取页面路径（去掉查询参数）
        const pagePath = banner.url.split('?')[0]

        if (tabBarPages.includes(pagePath)) {
          // 如果有查询参数，需要特殊处理
          if (banner.url.includes('?')) {
            const urlParams = new URLSearchParams(banner.url.split('?')[1])
            if (pagePath === '/pages/house/list') {
              // 房源列表页面的参数处理
              const filterData = {}
              urlParams.forEach((value, key) => {
                filterData[key] = value
              })
              uni.setStorageSync('houseListFilter', filterData)
            }
          }
          uni.switchTab({
            url: pagePath
          })
        } else {
          uni.navigateTo({
            url: banner.url
          })
        }
      }
    },

    // 跳转到房源列表
    toHouseList(type) {
      // 房源列表是 tabBar 页面，需要使用 switchTab
      if (type) {
        // 如果有类型参数，先存储到本地，然后在房源列表页面读取
        console.log('首页设置房源筛选类型:', type)
        uni.setStorageSync('houseListFilter', { type })

        // 显示提示信息
        const typeNames = {
          'whole': '整租',
          'shared': '合租',
          'single': '单间'
        }
        uni.showToast({
          title: `跳转到${typeNames[type] || type}房源`,
          icon: 'none',
          duration: 1500
        })
      }
      uni.switchTab({
        url: '/pages/house/list'
      })
    },

    // 地图找房 - 使用系统位置选择器
    toMap() {
      console.log('toMap 函数被调用')

      // 直接调用系统位置选择器
      uni.chooseLocation({
        success: (res) => {
          console.log('选择位置成功:', res)

          // 存储选择的位置信息，用于房源列表筛选
          const locationFilter = {
            latitude: res.latitude,
            longitude: res.longitude,
            address: res.address,
            name: res.name || '',
            city: this.parseCity(res.address),
            district: this.parseDistrict(res.address)
          }

          uni.setStorageSync('houseListLocationFilter', locationFilter)

          uni.showToast({
            title: `已选择：${res.name || res.address}`,
            icon: 'success',
            duration: 2000
          })

          // 跳转到房源列表页面，并应用位置筛选
          setTimeout(() => {
            uni.switchTab({
              url: '/pages/house/list'
            })
          }, 1500)
        },
        fail: (error) => {
          console.error('选择位置失败:', error)

          let errorMsg = '选择位置失败'
          if (error.errMsg.includes('cancel')) {
            errorMsg = '已取消选择位置'
          } else if (error.errMsg.includes('auth')) {
            errorMsg = '需要位置权限，请在小程序设置中开启'
          } else if (error.errMsg.includes('denied')) {
            errorMsg = '位置权限被拒绝'
          } else if (error.errMsg.includes('system permission denied')) {
            errorMsg = '系统位置权限未开启'
          }

          uni.showToast({
            title: errorMsg,
            icon: 'none',
            duration: 3000
          })

          // 如果是权限问题，提示用户手动开启
          if (error.errMsg.includes('auth') || error.errMsg.includes('denied')) {
            setTimeout(() => {
              uni.showModal({
                title: '位置权限',
                content: '需要位置权限来使用地图找房功能，请在小程序设置中开启位置权限',
                confirmText: '去设置',
                success: (modalRes) => {
                  if (modalRes.confirm) {
                    uni.openSetting()
                  }
                }
              })
            }, 1000)
          }
        }
      })
    },

    // 解析城市信息
    parseCity(address) {
      if (!address) return ''
      // 简单的城市解析逻辑，可以根据实际需要优化
      const cityMatch = address.match(/(.+?市)/)
      return cityMatch ? cityMatch[1] : ''
    },

    // 解析区县信息
    parseDistrict(address) {
      if (!address) return ''
      // 简单的区县解析逻辑，可以根据实际需要优化
      const districtMatch = address.match(/市(.+?区|.+?县)/)
      return districtMatch ? districtMatch[1] : ''
    },

    // 跳转到房源详情
    toHouseDetail(houseId) {
      uni.navigateTo({
        url: `/pages/house/detail?id=${houseId}`
      })
    }
  },

  onLoad() {
    // 获取系统信息，包括状态栏高度
    const systemInfo = uni.getSystemInfoSync()
    this.statusBarHeight = systemInfo.statusBarHeight || 44 // 默认44px

    // 获取用户位置
    const location = uni.getStorageSync('userLocation')
    if (location) {
      this.currentLocation = location.name
    }

    // 加载数据
    this.loadData()
  },

  onShow() {
    // 页面显示时刷新数据
    this.loadData()
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadData().finally(() => {
      uni.stopPullDownRefresh()
    })
  }
}
</script>

<style scoped>
.index-container {
  background: #f8fafc;
  min-height: 100vh;
}

/* 头部区域 */
.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 30rpx;
  padding-bottom: 100rpx;
  position: relative;
  overflow: hidden;
  border-radius: 0 0 40rpx 40rpx;
}

.header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>');
  pointer-events: none;
}



.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 40rpx;
  position: relative;
  z-index: 1;
}

.greeting-section {
  flex: 1;
  padding-right: 20rpx;
}

.greeting-text {
  display: block;
  font-size: 40rpx;
  font-weight: 700;
  color: #fff;
  margin-bottom: 12rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  line-height: 1.2;
}

.greeting-sub {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.85);
  font-weight: 400;
  line-height: 1.3;
}

.location-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 20rpx;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 25rpx;
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.location-btn:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.25);
}

.location-text {
  font-size: 26rpx;
  color: #fff;
  max-width: 120rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 500;
}

.search-section {
  position: relative;
  z-index: 1;
}

.search-bar {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 30rpx;
  padding: 28rpx 32rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
  min-height: 88rpx;
}

.search-bar:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
}

.search-placeholder {
  flex: 1;
  font-size: 28rpx;
  color: #666;
  font-weight: 400;
}

.search-btn {
  background: linear-gradient(135deg, #007aff 0%, #5856d6 100%);
  border-radius: 22rpx;
  padding: 16rpx 28rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.3);
  transition: all 0.3s ease;
}

.search-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.4);
}

.search-btn-text {
  font-size: 28rpx;
  color: #fff;
  font-weight: 600;
}

/* 轮播图区域 */
.banner-section {
  margin: 30rpx;
  margin-top: -80rpx;
  position: relative;
  z-index: 2;
}

.banner-swiper {
  height: 360rpx;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.12), 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.banner-item {
  position: relative;
  height: 100%;
  overflow: hidden;
}

.banner-image {
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease;
}

.banner-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
  padding: 40rpx 30rpx 30rpx;
  color: #fff;
}

.banner-title {
  display: block;
  font-size: 32rpx;
  font-weight: 700;
  margin-bottom: 8rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.banner-desc {
  display: block;
  font-size: 24rpx;
  opacity: 0.9;
  font-weight: 400;
}

/* 快捷入口区域 */
.quick-section {
  margin: 50rpx 30rpx;
}

.section-header {
  margin-bottom: 30rpx;
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 8rpx;
}

.section-subtitle {
  display: block;
  font-size: 26rpx;
  color: #718096;
  font-weight: 400;
}

.quick-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.quick-item {
  background: #fff;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid #f7fafc;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.quick-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #667eea, #764ba2);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.quick-item:active {
  transform: translateY(4rpx);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.quick-item:active::before {
  transform: scaleX(1);
}

.quick-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20rpx;
  position: relative;
}

.quick-icon.whole {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.quick-icon.shared {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.quick-icon.single {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.quick-icon.map {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.icon-emoji {
  font-size: 36rpx;
}

.quick-title {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 8rpx;
}

.quick-desc {
  display: block;
  font-size: 24rpx;
  color: #718096;
  font-weight: 400;
}

/* 内容区域通用样式 */
.recommend-section, .latest-section {
  margin: 50rpx 30rpx;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: 35rpx;
}

.header-left {
  flex: 1;
}

.content-title {
  display: block;
  font-size: 34rpx;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 10rpx;
  line-height: 1.2;
}

.content-subtitle {
  display: block;
  font-size: 26rpx;
  color: #718096;
  font-weight: 400;
  line-height: 1.3;
}

.more-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background: #f7fafc;
  border-radius: 20rpx;
  border: 1rpx solid #e2e8f0;
  transition: all 0.3s ease;
}

.more-btn:active {
  background: #edf2f7;
  transform: scale(0.95);
}

.more-text {
  font-size: 26rpx;
  color: #007aff;
  font-weight: 500;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 80rpx 0;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f4f6;
  border-top: 4rpx solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 26rpx;
  color: #9ca3af;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 推荐房源样式 */
.recommend-scroll {
  margin: 0 -30rpx;
  padding: 0 30rpx;
}

.recommend-list {
  display: flex;
  gap: 20rpx;
  padding-bottom: 20rpx;
}

.recommend-card {
  width: 280rpx;
  flex-shrink: 0;
  background: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid #f7fafc;
  transition: all 0.3s ease;
}

.recommend-card:active {
  transform: translateY(4rpx);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.card-image {
  position: relative;
  height: 200rpx;
  overflow: hidden;
}

.house-image {
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease;
}

.recommend-card:active .house-image {
  transform: scale(1.05);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.3));
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  padding: 20rpx;
}

.house-type-tag {
  background: rgba(0, 122, 255, 0.9);
  color: #fff;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 600;
  backdrop-filter: blur(10rpx);
}

.favorite-btn {
  width: 40rpx;
  height: 40rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10rpx);
}

.card-content {
  padding: 24rpx;
}

.card-title {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.card-tags {
  display: flex;
  gap: 8rpx;
  margin-bottom: 12rpx;
}

.tag {
  background: #f7fafc;
  color: #4a5568;
  padding: 6rpx 12rpx;
  border-radius: 8rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.card-location {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 16rpx;
}

.location-text {
  font-size: 24rpx;
  color: #718096;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.card-price {
  display: flex;
  align-items: baseline;
  gap: 4rpx;
}

.price {
  font-size: 32rpx;
  font-weight: 700;
  color: #e53e3e;
}

.price-unit {
  font-size: 24rpx;
  color: #718096;
  font-weight: 400;
}

/* 最新房源样式 */
.latest-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.latest-card {
  background: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid #f7fafc;
  transition: all 0.3s ease;
  display: flex;
}

.latest-card:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.card-image-container {
  position: relative;
  width: 240rpx;
  height: 180rpx;
  flex-shrink: 0;
}

.card-image {
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease;
}

.latest-card:active .card-image {
  transform: scale(1.05);
}

.image-badges {
  position: absolute;
  top: 12rpx;
  left: 12rpx;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.type-badge {
  background: rgba(0, 122, 255, 0.9);
  color: #fff;
  padding: 6rpx 12rpx;
  border-radius: 10rpx;
  font-size: 20rpx;
  font-weight: 600;
  backdrop-filter: blur(10rpx);
}

.new-badge {
  background: rgba(239, 68, 68, 0.9);
  color: #fff;
  padding: 6rpx 12rpx;
  border-radius: 10rpx;
  font-size: 20rpx;
  font-weight: 600;
  backdrop-filter: blur(10rpx);
}

.card-info {
  flex: 1;
  padding: 24rpx;
  display: flex;
  flex-direction: column;
}

.info-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12rpx;
}

.info-title {
  flex: 1;
  font-size: 28rpx;
  font-weight: 600;
  color: #2d3748;
  line-height: 1.4;
  margin-right: 16rpx;
}

.favorite-icon {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.info-desc {
  font-size: 24rpx;
  color: #718096;
  line-height: 1.5;
  margin-bottom: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.info-tags {
  display: flex;
  gap: 8rpx;
  margin-bottom: 12rpx;
}

.info-tag {
  background: #f7fafc;
  color: #4a5568;
  padding: 6rpx 12rpx;
  border-radius: 8rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.info-location {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 16rpx;
}

.location-detail {
  font-size: 24rpx;
  color: #718096;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

.info-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

.price-section {
  display: flex;
  align-items: baseline;
  gap: 4rpx;
}

.price-amount {
  font-size: 32rpx;
  font-weight: 700;
  color: #e53e3e;
}

.price-period {
  font-size: 24rpx;
  color: #718096;
  font-weight: 400;
}

.stats-section {
  display: flex;
  gap: 16rpx;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.stat-text {
  font-size: 22rpx;
  color: #9ca3af;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 40rpx;
  text-align: center;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
  opacity: 0.6;
}

.empty-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #4a5568;
  margin-bottom: 12rpx;
}

.empty-desc {
  font-size: 24rpx;
  color: #9ca3af;
  line-height: 1.5;
}

/* 底部间距 */
.bottom-spacing {
  height: 40rpx;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .quick-grid {
    grid-template-columns: 1fr;
  }

  .latest-card {
    flex-direction: column;
  }

  .card-image-container {
    width: 100%;
    height: 200rpx;
  }
}
</style>

.banner-swiper {
  height: 300rpx;
  margin: 20rpx;
  border-radius: 20rpx;
  overflow: hidden;
}

.banner-swiper image {
  width: 100%;
  height: 100%;
}

.quick-entry {
  background: #fff;
  margin: 20rpx;
  border-radius: 20rpx;
  padding: 40rpx 20rpx;
  display: flex;
  justify-content: space-around;
}

.entry-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15rpx;
}

.entry-icon {
  width: 80rpx;
  height: 80rpx;
  background: #f8f9fa;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.entry-text {
  font-size: 26rpx;
  color: #333;
}



.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
  padding: 0 10rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.more-btn {
  font-size: 26rpx;
  color: #007aff;
}

.recommend-list {
  white-space: nowrap;
  padding: 0 10rpx;
}

.recommend-item {
  display: inline-block;
  width: 280rpx;
  background: #fff;
  border-radius: 20rpx;
  margin-right: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  vertical-align: top;
}

.recommend-item .house-image {
  height: 200rpx;
  position: relative;
}

.recommend-item .house-image image {
  width: 100%;
  height: 100%;
}

.house-type {
  position: absolute;
  top: 15rpx;
  left: 15rpx;
  background: rgba(0, 122, 255, 0.9);
  color: #fff;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
}

.recommend-item .house-info {
  padding: 20rpx;
}

.house-title {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.house-tags {
  display: flex;
  gap: 8rpx;
  margin-bottom: 10rpx;
  flex-wrap: wrap;
}

.tag {
  background: #f0f0f0;
  color: #666;
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
  font-size: 22rpx;
}

.house-location {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.location-text {
  font-size: 24rpx;
  color: #999;
  margin-left: 6rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.price-info {
  display: flex;
  align-items: baseline;
}

.price {
  font-size: 30rpx;
  font-weight: bold;
  color: #ff6b6b;
}

.price-unit {
  font-size: 22rpx;
  color: #999;
  margin-left: 6rpx;
}

.latest-list {
  background: #fff;
  border-radius: 20rpx;
  overflow: hidden;
}

.house-item {
  display: flex;
  padding: 20rpx;
  border-bottom: 1rpx solid #f8f9fa;
}

.house-item:last-child {
  border-bottom: none;
}

.house-item .house-image {
  width: 200rpx;
  height: 150rpx;
  border-radius: 12rpx;
  overflow: hidden;
  margin-right: 20rpx;
  position: relative;
  flex-shrink: 0;
}

.house-item .house-image image {
  width: 100%;
  height: 100%;
}

.house-item .house-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.house-desc {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.house-bottom {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 10rpx;
}

.house-stats {
  display: flex;
  gap: 15rpx;
}

.stat-item {
  font-size: 22rpx;
  color: #999;
  display: flex;
  align-items: center;
  gap: 4rpx;
}

.loading-container {
  padding: 40rpx 0;
  text-align: center;
}

.empty-container {
  padding: 60rpx 0;
  text-align: center;
  background: #fff;
  border-radius: 20rpx;
}

.empty-image {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}
</style>
