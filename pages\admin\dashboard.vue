<template>
  <view class="admin-dashboard">
    <!-- 概览卡片 -->
    <uni-row :gutter="20" class="overview-section">
      <uni-col :span="6">
        <uni-card title="总用户数" is-full>
          <view class="stat-card">
            <view class="stat-number">{{ userStats.total || 0 }}</view>
            <view class="stat-label">
              <text>今日新增: {{ userStats.todayNew || 0 }}</text>
            </view>
          </view>
        </uni-card>
      </uni-col>
      <uni-col :span="6">
        <uni-card title="总房源数" is-full>
          <view class="stat-card">
            <view class="stat-number">{{ houseStats.total || 0 }}</view>
            <view class="stat-label">
              <text>待审核: {{ houseStats.pending || 0 }}</text>
            </view>
          </view>
        </uni-card>
      </uni-col>
      <uni-col :span="6">
        <uni-card title="学生用户" is-full>
          <view class="stat-card">
            <view class="stat-number">{{ userStats.students || 0 }}</view>
            <view class="stat-label">
              <text>占比: {{ getPercentage(userStats.students, userStats.total) }}%</text>
            </view>
          </view>
        </uni-card>
      </uni-col>
      <uni-col :span="6">
        <uni-card title="房东用户" is-full>
          <view class="stat-card">
            <view class="stat-number">{{ userStats.landlords || 0 }}</view>
            <view class="stat-label">
              <text>占比: {{ getPercentage(userStats.landlords, userStats.total) }}%</text>
            </view>
          </view>
        </uni-card>
      </uni-col>
    </uni-row>

    <!-- 快速操作 -->
    <uni-row :gutter="20" class="action-section">
      <uni-col :span="24">
        <uni-card title="快速操作" is-full>
          <view class="quick-actions">
            <button 
              class="action-btn" 
              type="primary"
              @click="navigateTo('/pages/admin/user-management')"
            >
              <uni-icons type="person" size="20" />
              <text>用户管理</text>
            </button>
            <button 
              class="action-btn" 
              type="primary"
              @click="navigateTo('/pages/admin/house-management')"
            >
              <uni-icons type="home" size="20" />
              <text>房源管理</text>
            </button>
            <button 
              class="action-btn" 
              type="primary"
              @click="navigateTo('/pages/admin/appointment-management')"
            >
              <uni-icons type="calendar" size="20" />
              <text>预约管理</text>
            </button>
            <button 
              class="action-btn" 
              type="primary"
              @click="navigateTo('/pages/admin/message-management')"
            >
              <uni-icons type="chat" size="20" />
              <text>消息管理</text>
            </button>
            <button 
              class="action-btn" 
              type="primary"
              @click="navigateTo('/pages/admin/report-management')"
            >
              <uni-icons type="flag" size="20" />
              <text>举报管理</text>
            </button>
            <button 
              class="action-btn" 
              type="primary"
              @click="navigateTo('/pages/admin/system-config')"
            >
              <uni-icons type="gear" size="20" />
              <text>系统配置</text>
            </button>
          </view>
        </uni-card>
      </uni-col>
    </uni-row>

    <!-- 统计详情 -->
    <uni-row :gutter="20" class="detail-section">
      <uni-col :span="8">
        <uni-card title="用户统计" is-full>
          <view class="detail-stats">
            <view class="stat-item">
              <text class="label">学生用户:</text>
              <text class="value">{{ userStats.students || 0 }}</text>
            </view>
            <view class="stat-item">
              <text class="label">房东用户:</text>
              <text class="value">{{ userStats.landlords || 0 }}</text>
            </view>
            <view class="stat-item">
              <text class="label">活跃用户:</text>
              <text class="value">{{ userStats.active || 0 }}</text>
            </view>
            <view class="stat-item">
              <text class="label">本月新增:</text>
              <text class="value">{{ userStats.monthNew || 0 }}</text>
            </view>
          </view>
        </uni-card>
      </uni-col>
      <uni-col :span="8">
        <uni-card title="房源统计" is-full>
          <view class="detail-stats">
            <view class="stat-item">
              <text class="label">已审核:</text>
              <text class="value">{{ houseStats.verified || 0 }}</text>
            </view>
            <view class="stat-item">
              <text class="label">待审核:</text>
              <text class="value">{{ houseStats.pending || 0 }}</text>
            </view>
            <view class="stat-item">
              <text class="label">可租房源:</text>
              <text class="value">{{ houseStats.available || 0 }}</text>
            </view>
            <view class="stat-item">
              <text class="label">已租房源:</text>
              <text class="value">{{ houseStats.rented || 0 }}</text>
            </view>
          </view>
        </uni-card>
      </uni-col>
      <uni-col :span="8">
        <uni-card title="系统状态" is-full>
          <view class="detail-stats">
            <view class="stat-item">
              <text class="label">在线用户:</text>
              <text class="value">{{ systemStats.onlineUsers || 0 }}</text>
            </view>
            <view class="stat-item">
              <text class="label">今日访问:</text>
              <text class="value">{{ systemStats.todayVisits || 0 }}</text>
            </view>
            <view class="stat-item">
              <text class="label">系统运行:</text>
              <text class="value success">正常</text>
            </view>
            <view class="stat-item">
              <text class="label">数据库:</text>
              <text class="value success">正常</text>
            </view>
          </view>
        </uni-card>
      </uni-col>
    </uni-row>
  </view>
</template>

<script>
export default {
  data() {
    return {
      loading: false,
      userStats: {},
      houseStats: {},
      systemStats: {}
    }
  },
  
  onLoad() {
    this.loadStatistics();
  },
  
  onShow() {
    // 页面显示时刷新数据
    this.loadStatistics();
  },
  
  methods: {
    // 加载统计数据
    async loadStatistics() {
      try {
        // 加载用户统计
        const userRes = await uniCloud.callFunction({
          name: 'admin-management',
          data: {
            action: 'getUserStatistics'
          }
        });
        
        if (userRes.result.code === 0) {
          this.userStats = userRes.result.data;
        }
        
        // 模拟房源统计数据（实际项目中应该从云函数获取）
        this.houseStats = {
          total: 156,
          verified: 142,
          pending: 14,
          available: 89,
          rented: 53
        };
        
        // 模拟系统统计数据
        this.systemStats = {
          onlineUsers: 23,
          todayVisits: 456
        };
        
      } catch (error) {
        console.error('加载统计数据失败:', error);
        uni.showToast({
          title: '加载统计数据失败',
          icon: 'error'
        });
      }
    },
    
    // 导航到指定页面
    navigateTo(url) {
      uni.navigateTo({ url });
    },
    
    // 计算百分比
    getPercentage(part, total) {
      if (!total || total === 0) return 0;
      return Math.round((part / total) * 100);
    }
  }
}
</script>

<style scoped>
.admin-dashboard {
  padding: 20px;
  background-color: #f5f5f5;
}

.overview-section {
  margin-bottom: 20px;
}

.action-section {
  margin-bottom: 20px;
}

.detail-section {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
  padding: 20px;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #007aff;
  margin-bottom: 10px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.quick-actions {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
  padding: 20px;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 20px;
  border-radius: 8px;
  border: none;
  background-color: #007aff;
  color: white;
  font-size: 14px;
}

.detail-stats {
  padding: 20px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #eee;
}

.stat-item:last-child {
  border-bottom: none;
}

.label {
  color: #666;
  font-size: 14px;
}

.value {
  color: #333;
  font-weight: bold;
  font-size: 16px;
}

.value.success {
  color: #52c41a;
}
</style>
