'use strict';

exports.main = async (event, context) => {
  console.log('检查用户列表...');
  
  try {
    const db = uniCloud.database();
    
    // 获取所有用户
    const allUsers = await db.collection('uni-id-users').get();
    
    console.log('用户总数:', allUsers.data.length);
    
    const userList = allUsers.data.map(user => ({
      id: user._id,
      username: user.username,
      nickname: user.nickname,
      role: user.role,
      status: user.status
    }));
    
    return {
      code: 0,
      message: '用户列表获取成功',
      data: {
        total: allUsers.data.length,
        users: userList
      }
    };
    
  } catch (error) {
    console.error('检查用户失败:', error);
    return {
      code: -1,
      message: '检查失败',
      error: error.message
    };
  }
};
