<template>
  <view class="test-container">
    <view class="test-header">
      <text class="test-title">搜索功能测试</text>
      <text class="test-subtitle">验证搜索跳转和界面效果</text>
    </view>
    
    <view class="test-content">
      <!-- 测试项目 -->
      <view class="test-section">
        <view class="section-title">🧪 功能测试</view>
        
        <view class="test-item">
          <view class="test-info">
            <text class="test-name">首页搜索栏点击</text>
            <text class="test-desc">点击首页搜索栏应跳转到搜索页面</text>
          </view>
          <button class="test-btn" @click="testIndexSearch">测试</button>
        </view>
        
        <view class="test-item">
          <view class="test-info">
            <text class="test-name">直接访问搜索页</text>
            <text class="test-desc">直接跳转到搜索页面</text>
          </view>
          <button class="test-btn" @click="testDirectSearch">测试</button>
        </view>
        
        <view class="test-item">
          <view class="test-info">
            <text class="test-name">搜索页面样式</text>
            <text class="test-desc">检查搜索页面的现代化样式</text>
          </view>
          <button class="test-btn" @click="testSearchStyle">测试</button>
        </view>
      </view>
      
      <!-- 预期效果 -->
      <view class="expected-section">
        <view class="section-title">✅ 预期效果</view>
        
        <view class="expected-list">
          <view class="expected-item">
            <text class="expected-text">• 首页搜索栏具有渐变背景和毛玻璃效果</text>
          </view>
          <view class="expected-item">
            <text class="expected-text">• 点击搜索栏能正常跳转到搜索页面</text>
          </view>
          <view class="expected-item">
            <text class="expected-text">• 搜索页面有自定义导航栏</text>
          </view>
          <view class="expected-item">
            <text class="expected-text">• 搜索输入框有聚焦动画效果</text>
          </view>
          <view class="expected-item">
            <text class="expected-text">• 搜索建议和历史记录样式美观</text>
          </view>
        </view>
      </view>
      
      <!-- 操作按钮 -->
      <view class="test-actions">
        <button class="action-btn primary" @click="goToIndex">
          返回首页测试
        </button>
        <button class="action-btn" @click="goBack">
          返回
        </button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  methods: {
    testIndexSearch() {
      uni.showToast({
        title: '跳转到首页测试搜索栏',
        icon: 'none'
      });
      
      setTimeout(() => {
        uni.switchTab({
          url: '/pages/index/index'
        });
      }, 1000);
    },
    
    testDirectSearch() {
      uni.navigateTo({
        url: '/pages/search/search'
      });
    },
    
    testSearchStyle() {
      uni.showModal({
        title: '样式检查',
        content: '即将跳转到搜索页面，请检查：\n1. 渐变背景\n2. 毛玻璃效果\n3. 动画过渡\n4. 交互反馈',
        success: (res) => {
          if (res.confirm) {
            uni.navigateTo({
              url: '/pages/search/search'
            });
          }
        }
      });
    },
    
    goToIndex() {
      uni.switchTab({
        url: '/pages/index/index'
      });
    },
    
    goBack() {
      uni.navigateBack();
    }
  }
}
</script>

<style scoped>
.test-container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  padding: 40rpx;
}

.test-header {
  text-align: center;
  margin-bottom: 60rpx;
  padding-top: 60rpx;
}

.test-title {
  display: block;
  font-size: 40rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 20rpx;
}

.test-subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.test-content {
  background: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.test-section, .expected-section {
  margin-bottom: 60rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #303133;
  margin-bottom: 30rpx;
}

.test-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
}

.test-info {
  flex: 1;
}

.test-name {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8rpx;
}

.test-desc {
  display: block;
  font-size: 24rpx;
  color: #909399;
  line-height: 1.4;
}

.test-btn {
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
}

.expected-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.expected-item {
  padding: 16rpx 20rpx;
  background: #f0f9ff;
  border-radius: 12rpx;
  border-left: 4rpx solid #007aff;
}

.expected-text {
  font-size: 26rpx;
  color: #303133;
  line-height: 1.5;
}

.test-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  height: 88rpx;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #f5f7fa;
  color: #606266;
}

.action-btn.primary {
  background: #007aff;
  color: #fff;
}

.action-btn:active {
  opacity: 0.8;
}
</style>
