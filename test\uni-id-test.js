// 测试 uni-id-common 的 API 调用方式
const uniID = require('uni-id-common');

// 模拟 context
const mockContext = {
  APPID: '__UNI__080E7BD',
  PLATFORM: 'mp-weixin',
  LOCALE: 'zh-Hans',
  CLIENTIP: '127.0.0.1',
  DEVICEID: 'test-device'
};

// 测试创建实例
function testCreateInstance() {
  try {
    const uniIdIns = uniID.createInstance({ context: mockContext });
    console.log('创建实例成功:', typeof uniIdIns);
    console.log('实例方法:', Object.getOwnPropertyNames(uniIdIns));
    
    // 检查是否有 register 方法
    if (typeof uniIdIns.register === 'function') {
      console.log('register 方法存在');
    } else {
      console.log('register 方法不存在');
      console.log('可用的方法:', Object.keys(uniIdIns).filter(key => typeof uniIdIns[key] === 'function'));
    }
    
    return uniIdIns;
  } catch (error) {
    console.error('创建实例失败:', error);
    return null;
  }
}

// 测试注册方法
async function testRegister(uniIdIns) {
  if (!uniIdIns) return;
  
  try {
    const testData = {
      username: 'testuser',
      password: '123456',
      mobile: '13800138000',
      role: ['student']
    };
    
    console.log('测试注册数据:', testData);
    
    // 尝试不同的调用方式
    console.log('方式1: 直接调用 register');
    const result1 = await uniIdIns.register(testData);
    console.log('注册结果1:', result1);
    
  } catch (error) {
    console.error('注册测试失败:', error);
    
    // 尝试其他可能的调用方式
    try {
      console.log('方式2: 使用 uniID.register');
      const result2 = await uniID.register(testData);
      console.log('注册结果2:', result2);
    } catch (error2) {
      console.error('方式2也失败:', error2);
    }
  }
}

// 运行测试
async function runTest() {
  console.log('开始测试 uni-id-common...');
  
  const uniIdIns = testCreateInstance();
  await testRegister(uniIdIns);
  
  console.log('测试完成');
}

// 如果直接运行此文件
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { runTest };
}

// 在浏览器环境中运行
if (typeof window !== 'undefined') {
  window.runUniIdTest = runTest;
}
