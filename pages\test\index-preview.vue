<template>
  <view class="preview-container">
    <view class="preview-header">
      <text class="preview-title">首页美化预览</text>
      <text class="preview-subtitle">查看全新设计的首页效果</text>
    </view>
    
    <view class="preview-content">
      <!-- 美化亮点 -->
      <view class="highlights-section">
        <view class="highlights-title">✨ 美化亮点</view>
        
        <view class="highlight-item">
          <view class="highlight-icon">🎨</view>
          <view class="highlight-text">
            <text class="highlight-name">现代化设计</text>
            <text class="highlight-desc">渐变背景、毛玻璃效果、圆角卡片</text>
          </view>
        </view>
        
        <view class="highlight-item">
          <view class="highlight-icon">🔍</view>
          <view class="highlight-text">
            <text class="highlight-name">搜索栏优化</text>
            <text class="highlight-desc">个性化问候语、美观的搜索按钮</text>
          </view>
        </view>
        
        <view class="highlight-item">
          <view class="highlight-icon">🏠</view>
          <view class="highlight-text">
            <text class="highlight-name">快捷入口重设计</text>
            <text class="highlight-desc">emoji图标、渐变背景、悬停效果</text>
          </view>
        </view>
        
        <view class="highlight-item">
          <view class="highlight-icon">📱</view>
          <view class="highlight-text">
            <text class="highlight-name">卡片式布局</text>
            <text class="highlight-desc">房源信息卡片化展示，层次分明</text>
          </view>
        </view>
        
        <view class="highlight-item">
          <view class="highlight-icon">🎯</view>
          <view class="highlight-text">
            <text class="highlight-name">交互优化</text>
            <text class="highlight-desc">点击动画、加载状态、空状态设计</text>
          </view>
        </view>
      </view>
      
      <!-- 设计特色 -->
      <view class="features-section">
        <view class="features-title">🎪 设计特色</view>
        
        <view class="feature-grid">
          <view class="feature-card">
            <text class="feature-emoji">🌈</text>
            <text class="feature-name">渐变色彩</text>
            <text class="feature-desc">紫色渐变主题</text>
          </view>
          
          <view class="feature-card">
            <text class="feature-emoji">💎</text>
            <text class="feature-name">毛玻璃效果</text>
            <text class="feature-desc">现代化视觉</text>
          </view>
          
          <view class="feature-card">
            <text class="feature-emoji">🎭</text>
            <text class="feature-name">动画过渡</text>
            <text class="feature-desc">流畅交互</text>
          </view>
          
          <view class="feature-card">
            <text class="feature-emoji">📐</text>
            <text class="feature-name">网格布局</text>
            <text class="feature-desc">整齐排列</text>
          </view>
        </view>
      </view>
      
      <!-- 对比说明 */
      <view class="comparison-section">
        <view class="comparison-title">📊 改进对比</view>
        
        <view class="comparison-item">
          <view class="comparison-label before">改进前</view>
          <text class="comparison-text">简单的搜索栏，普通的图标按钮</text>
        </view>
        
        <view class="comparison-item">
          <view class="comparison-label after">改进后</view>
          <text class="comparison-text">个性化问候语，渐变搜索栏，emoji图标</text>
        </view>
        
        <view class="comparison-item">
          <view class="comparison-label before">改进前</view>
          <text class="comparison-text">平铺式房源列表，信息密集</text>
        </view>
        
        <view class="comparison-item">
          <view class="comparison-label after">改进后</view>
          <text class="comparison-text">卡片式设计，信息层次清晰</text>
        </view>
      </view>
      
      <!-- 测试按钮 -->
      <view class="test-section">
        <view class="test-title">🧪 体验测试</view>
        
        <button class="test-btn primary" @click="goToIndex">
          查看美化后的首页
        </button>
        
        <button class="test-btn" @click="testSearch">
          测试搜索功能
        </button>
        
        <button class="test-btn" @click="testHouseList">
          查看房源列表
        </button>
        
        <button class="test-btn" @click="goBack">
          返回
        </button>
      </view>
      
      <!-- 技术说明 -->
      <view class="tech-section">
        <view class="tech-title">⚙️ 技术实现</view>
        
        <view class="tech-list">
          <text class="tech-item">• CSS Grid 网格布局</text>
          <text class="tech-item">• Linear-gradient 渐变背景</text>
          <text class="tech-item">• Backdrop-filter 毛玻璃效果</text>
          <text class="tech-item">• Transform 动画变换</text>
          <text class="tech-item">• Box-shadow 阴影效果</text>
          <text class="tech-item">• Flex 弹性布局</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  methods: {
    goToIndex() {
      uni.switchTab({
        url: '/pages/index/index'
      });
    },
    
    testSearch() {
      uni.navigateTo({
        url: '/pages/search/search'
      });
    },
    
    testHouseList() {
      uni.switchTab({
        url: '/pages/house/list'
      });
    },
    
    goBack() {
      uni.navigateBack();
    }
  }
}
</script>

<style scoped>
.preview-container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  padding: 40rpx;
}

.preview-header {
  text-align: center;
  margin-bottom: 60rpx;
  padding-top: 60rpx;
}

.preview-title {
  display: block;
  font-size: 40rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 20rpx;
}

.preview-subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.preview-content {
  background: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  gap: 50rpx;
}

.highlights-section, .features-section, .comparison-section, .test-section, .tech-section {
  display: flex;
  flex-direction: column;
}

.highlights-title, .features-title, .comparison-title, .test-title, .tech-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #303133;
  margin-bottom: 30rpx;
}

.highlight-item {
  display: flex;
  align-items: flex-start;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
}

.highlight-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
  margin-top: 4rpx;
}

.highlight-text {
  flex: 1;
}

.highlight-name {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8rpx;
}

.highlight-desc {
  display: block;
  font-size: 24rpx;
  color: #909399;
  line-height: 1.4;
}

.feature-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
}

.feature-card {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx;
  text-align: center;
}

.feature-emoji {
  display: block;
  font-size: 32rpx;
  margin-bottom: 12rpx;
}

.feature-name {
  display: block;
  font-size: 26rpx;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8rpx;
}

.feature-desc {
  display: block;
  font-size: 22rpx;
  color: #909399;
}

.comparison-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.comparison-label {
  width: 120rpx;
  font-size: 24rpx;
  font-weight: 600;
  padding: 8rpx 16rpx;
  border-radius: 6rpx;
  text-align: center;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.comparison-label.before {
  background: #fee2e2;
  color: #dc2626;
}

.comparison-label.after {
  background: #dcfce7;
  color: #16a34a;
}

.comparison-text {
  flex: 1;
  font-size: 26rpx;
  color: #606266;
  line-height: 1.4;
}

.test-btn {
  width: 100%;
  height: 88rpx;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #f5f7fa;
  color: #606266;
  margin-bottom: 16rpx;
}

.test-btn.primary {
  background: #007aff;
  color: #fff;
}

.test-btn:active {
  opacity: 0.8;
}

.tech-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.tech-item {
  font-size: 26rpx;
  color: #303133;
  line-height: 1.6;
  padding: 12rpx 16rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
}
</style>
