// 预约相关API
import { callAPI } from './index.js'

/**
 * 创建预约
 * @param {Object} appointmentData 预约数据
 * @param {string} appointmentData.houseId 房源ID
 * @param {string} appointmentData.appointmentDate 预约时间
 * @param {string} appointmentData.contactName 联系人姓名
 * @param {string} appointmentData.contactPhone 联系电话
 * @param {string} appointmentData.message 预约留言
 */
export function createAppointment(appointmentData) {
  return callAPI('appointment-management', 'createAppointment', appointmentData)
}

/**
 * 更新预约
 * @param {string} appointmentId 预约ID
 * @param {Object} updateData 更新数据
 */
export function updateAppointment(appointmentId, updateData) {
  return callAPI('appointment-management', 'updateAppointment', {
    appointmentId,
    ...updateData
  })
}

/**
 * 取消预约
 * @param {string} appointmentId 预约ID
 * @param {string} reason 取消原因
 */
export function cancelAppointment(appointmentId, reason = '') {
  return callAPI('appointment-management', 'cancelAppointment', {
    appointmentId,
    reason
  })
}

/**
 * 确认预约
 * @param {string} appointmentId 预约ID
 * @param {string} notes 备注信息
 */
export function confirmAppointment(appointmentId, notes = '') {
  return callAPI('appointment-management', 'confirmAppointment', {
    appointmentId,
    notes
  })
}

/**
 * 拒绝预约
 * @param {string} appointmentId 预约ID
 * @param {string} reason 拒绝原因
 */
export function rejectAppointment(appointmentId, reason) {
  return callAPI('appointment-management', 'rejectAppointment', {
    appointmentId,
    reason
  })
}

/**
 * 完成预约
 * @param {string} appointmentId 预约ID
 * @param {string} notes 备注信息
 */
export function completeAppointment(appointmentId, notes = '') {
  return callAPI('appointment-management', 'completeAppointment', {
    appointmentId,
    notes
  })
}

/**
 * 获取我的预约列表
 * @param {Object} params 查询参数
 * @param {number} params.page 页码
 * @param {number} params.pageSize 每页数量
 * @param {string} params.status 状态筛选
 */
export function getMyAppointments(params = {}) {
  return callAPI('appointment-management', 'getMyAppointments', params)
}

/**
 * 获取房东的预约列表
 * @param {Object} params 查询参数
 * @param {number} params.page 页码
 * @param {number} params.pageSize 每页数量
 * @param {string} params.status 状态筛选
 */
export function getLandlordAppointments(params = {}) {
  return callAPI('appointment-management', 'getLandlordAppointments', params)
}

/**
 * 获取预约详情
 * @param {string} appointmentId 预约ID
 */
export function getAppointmentDetail(appointmentId) {
  return callAPI('appointment-management', 'getAppointmentDetail', { appointmentId })
}

/**
 * 获取预约统计
 */
export function getAppointmentStats() {
  return callAPI('appointment-management', 'getAppointmentStats', {})
}

/**
 * 检查预约冲突
 * @param {string} houseId 房源ID
 * @param {string} appointmentDate 预约时间
 */
export function checkAppointmentConflict(houseId, appointmentDate) {
  return callAPI('appointment-management', 'checkAppointmentConflict', {
    houseId,
    appointmentDate
  })
}
