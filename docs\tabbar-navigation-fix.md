# TabBar 导航修复说明

## 🐛 问题描述

在微信小程序中，尝试使用 `uni.navigateTo` 跳转到 tabBar 页面会报错：

```
Error: MiniProgramError
{"errMsg":"navigateTo:fail can not navigateTo a tabbar page"}
```

## 📋 TabBar 页面列表

根据 `pages.json` 配置，以下页面是 tabBar 页面：

- `pages/index/index` - 首页
- `pages/house/list` - 房源列表
- `pages/search/search` - 搜索
- `pages/user/profile` - 个人中心

## 🔧 修复内容

### 1. 首页房源列表跳转修复

**修复前：**
```javascript
// 跳转到房源列表
toHouseList(type) {
  let url = '/pages/house/list'
  if (type) {
    url += `?type=${type}`
  }
  uni.navigateTo({  // ❌ 错误：不能用 navigateTo 跳转 tabBar 页面
    url: url
  })
}
```

**修复后：**
```javascript
// 跳转到房源列表
toHouseList(type) {
  // 房源列表是 tabBar 页面，需要使用 switchTab
  if (type) {
    // 如果有类型参数，先存储到本地，然后在房源列表页面读取
    uni.setStorageSync('houseListFilter', { type })
  }
  uni.switchTab({  // ✅ 正确：使用 switchTab 跳转 tabBar 页面
    url: '/pages/house/list'
  })
}
```

### 2. 轮播图点击跳转修复

**修复前：**
```javascript
// 轮播图点击
onBannerClick(banner) {
  if (banner.url) {
    uni.navigateTo({  // ❌ 可能错误：如果 URL 是 tabBar 页面会报错
      url: banner.url
    })
  }
}
```

**修复后：**
```javascript
// 轮播图点击
onBannerClick(banner) {
  if (banner.url) {
    // 检查是否是 tabBar 页面
    const tabBarPages = [
      '/pages/index/index',
      '/pages/house/list',
      '/pages/search/search',
      '/pages/user/profile'
    ]
    
    // 提取页面路径（去掉查询参数）
    const pagePath = banner.url.split('?')[0]
    
    if (tabBarPages.includes(pagePath)) {
      // 如果有查询参数，需要特殊处理
      if (banner.url.includes('?')) {
        const urlParams = new URLSearchParams(banner.url.split('?')[1])
        if (pagePath === '/pages/house/list') {
          // 房源列表页面的参数处理
          const filterData = {}
          urlParams.forEach((value, key) => {
            filterData[key] = value
          })
          uni.setStorageSync('houseListFilter', filterData)
        }
      }
      uni.switchTab({  // ✅ 正确：使用 switchTab 跳转 tabBar 页面
        url: pagePath
      })
    } else {
      uni.navigateTo({  // ✅ 正确：非 tabBar 页面使用 navigateTo
        url: banner.url
      })
    }
  }
}
```

### 3. 房源列表页面参数接收

**修复前：**
```javascript
onLoad() {
  this.loadHouseList(true)
}

onShow() {
  if (this.houseList.length > 0) {
    this.loadHouseList(true)
  }
}
```

**修复后：**
```javascript
onLoad(options) {
  // 处理 URL 参数
  if (options.type) {
    this.filters.type = options.type
  }
  
  this.loadHouseList(true)
}

onShow() {
  // 检查是否有从首页传递的筛选参数
  const houseListFilter = uni.getStorageSync('houseListFilter')
  if (houseListFilter) {
    // 应用筛选参数
    Object.assign(this.filters, houseListFilter)
    // 清除存储的参数，避免重复应用
    uni.removeStorageSync('houseListFilter')
    // 重新加载列表
    this.loadHouseList(true)
    return
  }
  
  // 从发布页返回时刷新列表
  if (this.houseList.length > 0) {
    this.loadHouseList(true)
  }
}
```

## 📚 导航方法对比

| 方法 | 适用场景 | 特点 |
|------|----------|------|
| `uni.navigateTo` | 普通页面跳转 | 可传递 URL 参数，支持返回 |
| `uni.switchTab` | tabBar 页面跳转 | 不支持 URL 参数，会关闭其他页面 |
| `uni.redirectTo` | 页面重定向 | 关闭当前页面，跳转到新页面 |
| `uni.reLaunch` | 重启应用 | 关闭所有页面，跳转到新页面 |
| `uni.navigateBack` | 返回上一页 | 返回到上一个页面 |

## 🎯 最佳实践

### 1. 跳转前检查页面类型

```javascript
// 通用跳转方法
function navigateToPage(url) {
  const tabBarPages = [
    '/pages/index/index',
    '/pages/house/list',
    '/pages/search/search',
    '/pages/user/profile'
  ]
  
  const pagePath = url.split('?')[0]
  
  if (tabBarPages.includes(pagePath)) {
    // 处理参数
    if (url.includes('?')) {
      // 将参数存储到本地存储
      const params = new URLSearchParams(url.split('?')[1])
      const paramObj = {}
      params.forEach((value, key) => {
        paramObj[key] = value
      })
      uni.setStorageSync(`${pagePath.replace(/\//g, '_')}_params`, paramObj)
    }
    
    uni.switchTab({ url: pagePath })
  } else {
    uni.navigateTo({ url })
  }
}
```

### 2. 参数传递策略

对于 tabBar 页面的参数传递：

1. **简单参数**：使用本地存储 `uni.setStorageSync`
2. **复杂参数**：使用全局状态管理
3. **临时参数**：在目标页面的 `onShow` 中读取并清除

### 3. 错误处理

```javascript
function safeNavigate(url) {
  try {
    // 先尝试 navigateTo
    uni.navigateTo({ url })
  } catch (error) {
    if (error.errMsg && error.errMsg.includes('tabbar page')) {
      // 如果是 tabBar 页面错误，改用 switchTab
      const pagePath = url.split('?')[0]
      uni.switchTab({ url: pagePath })
    } else {
      console.error('导航失败:', error)
    }
  }
}
```

## ✅ 修复验证

修复后，以下操作应该正常工作：

1. ✅ 首页快捷入口跳转到房源列表
2. ✅ 首页"更多"按钮跳转到房源列表
3. ✅ 轮播图点击跳转（支持 tabBar 和普通页面）
4. ✅ 房源列表正确接收筛选参数
5. ✅ 测试页面的导航功能

## 🔍 相关文件

- `pages/index/index.vue` - 首页导航修复
- `pages/house/list.vue` - 房源列表参数接收
- `pages.json` - tabBar 配置
- `docs/tabbar-navigation-fix.md` - 本修复说明文档
