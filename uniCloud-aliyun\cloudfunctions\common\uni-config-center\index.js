const fs = require('fs')
const path = require('path')

const configCenter = {
  config: {},
  
  hasItem(key) {
    return this.config.hasOwnProperty(key)
  },
  
  getItem(key) {
    return this.config[key]
  },
  
  setItem(key, value) {
    this.config[key] = value
  },
  
  removeItem(key) {
    delete this.config[key]
  },
  
  clear() {
    this.config = {}
  }
}

// 加载配置文件
function loadConfig() {
  try {
    // 加载 uni-id 配置
    const uniIdConfigPath = path.join(__dirname, 'uni-id', 'config.json')
    if (fs.existsSync(uniIdConfigPath)) {
      const uniIdConfig = JSON.parse(fs.readFileSync(uniIdConfigPath, 'utf8'))
      configCenter.setItem('uni-id', uniIdConfig)
    }
  } catch (error) {
    console.error('加载配置文件失败:', error)
  }
}

// 初始化时加载配置
loadConfig()

module.exports = configCenter
