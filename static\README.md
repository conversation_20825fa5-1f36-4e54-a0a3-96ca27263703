# 静态资源目录

这个目录用于存放应用的静态资源文件，包括图片、图标等。

## 📁 目录结构

```
static/
├── README.md           # 本说明文件
├── logo.png           # 应用Logo (120x120px)
├── icon.png           # 应用图标 (512x512px)
├── default-avatar.png # 默认头像 (200x200px)
├── default-house.png  # 默认房源图片 (400x300px)
├── empty-default.png  # 通用空状态图片 (200x200px)
├── empty-house.png    # 无房源状态图片 (200x200px)
├── empty-favorite.png # 无收藏状态图片 (200x200px)
├── empty-search.png   # 搜索无结果图片 (200x200px)
├── banner1.jpg        # 轮播图1 (750x300px)
├── banner2.jpg        # 轮播图2 (750x300px)
├── banner3.jpg        # 轮播图3 (750x300px)
├── map-marker.png     # 地图标记 (30x30px)
└── test-house.jpg     # 测试房源图片 (400x300px)
```

## 🔧 图片配置

### 文件格式要求
- **Logo/图标**: PNG格式，支持透明背景
- **照片**: JPG格式，质量80-90%
- **插画**: PNG格式，支持透明背景

### 尺寸规范
- **Logo**: 正方形，120x120px
- **轮播图**: 宽高比2.5:1，750x300px
- **房源图片**: 宽高比4:3，400x300px
- **头像**: 正方形，200x200px
- **空状态图**: 正方形，200x200px

### 文件大小限制
- 单张图片不超过500KB
- Logo和图标不超过100KB
- 轮播图不超过200KB

## 📥 下载图片资源

### 方法1: 自动下载（推荐）
```bash
# 在项目根目录运行
node scripts/download-images.js download
```

### 方法2: 手动下载
请参考 `docs/manual-download-guide.md` 文件中的详细指南。

### 方法3: 使用占位符
如果暂时无法下载图片，可以使用在线占位符服务：

```javascript
// 在 utils/image-config.js 中临时使用
export const IMAGE_CONFIG = {
  logo: 'https://via.placeholder.com/120x120/007aff/ffffff?text=Logo',
  defaultAvatar: 'https://via.placeholder.com/200x200/cccccc/ffffff?text=Avatar',
  defaultHouse: 'https://via.placeholder.com/400x300/f0f0f0/666666?text=House',
  // ... 其他图片
};
```

## 🔍 验证图片

### 检查图片完整性
```bash
node scripts/download-images.js check
```

### 生成图片清单
```bash
node scripts/download-images.js manifest
```

## 📱 在小程序中使用

### 基本用法
```javascript
// 导入图片配置
import { ImageUtils } from '@/utils/image-config.js'

// 获取头像
const avatar = ImageUtils.getAvatar(user.avatar)

// 获取房源图片
const houseImage = ImageUtils.getHouseImage(house.images)

// 获取空状态图片
const emptyImage = ImageUtils.getEmptyImage('house')
```

### 图片懒加载
```vue
<template>
  <image 
    :src="imageUrl" 
    mode="aspectFill"
    lazy-load
    @load="onImageLoad"
    @error="onImageError"
  />
</template>

<script>
import { imageLazyLoadMixin } from '@/utils/image-config.js'

export default {
  mixins: [imageLazyLoadMixin],
  // ...
}
</script>
```

## 🎨 图片优化建议

### 性能优化
1. 使用适当的图片格式
2. 控制图片文件大小
3. 实现图片懒加载
4. 提供多种尺寸版本

### 用户体验
1. 提供占位图显示
2. 图片加载失败的备用方案
3. 支持图片预览和缩放
4. 合理的图片压缩比例

## 🔗 相关文档

- [图片资源配置指南](../docs/image-resources.md)
- [手动下载指南](../docs/manual-download-guide.md)
- [图片工具类文档](../utils/image-config.js)

## ⚠️ 注意事项

1. **版权问题**: 确保使用的图片支持商业使用
2. **文件命名**: 严格按照配置文件中的命名规范
3. **尺寸要求**: 图片尺寸必须符合设计要求
4. **文件大小**: 控制文件大小以提升加载速度
5. **备份管理**: 建议备份原始图片文件

## 🆘 常见问题

### Q: 图片无法显示？
A: 检查文件名是否正确，路径是否存在，文件格式是否支持

### Q: 图片加载很慢？
A: 检查图片文件大小，考虑压缩或使用CDN

### Q: 如何批量处理图片？
A: 使用提供的脚本工具或在线批处理工具

### Q: 可以使用网络图片吗？
A: 可以，但建议下载到本地以提升加载速度和稳定性
