<template>
  <view class="search-container">
    <!-- 搜索栏 -->
    <view class="search-bar" :style="{ paddingTop: (statusBarHeight * 2 + 60) + 'rpx' }">
      <view class="search-input-wrapper">
        <uni-icons type="search" size="18" color="#999"></uni-icons>
        <input 
          class="search-input" 
          type="text" 
          placeholder="搜索房源、地址、关键词" 
          v-model="keyword"
          @input="onInput"
          @confirm="handleSearch"
          focus
        />
        <view class="clear-btn" v-if="keyword" @click="clearKeyword">
          <uni-icons type="clear" size="16" color="#999"></uni-icons>
        </view>
      </view>
      <text class="cancel-btn" @click="goBack">取消</text>
    </view>
    
    <!-- 搜索建议 -->
    <view class="suggestions" v-if="showSuggestions && suggestions.length > 0">
      <view 
        class="suggestion-item" 
        v-for="(item, index) in suggestions" 
        :key="index"
        @click="selectSuggestion(item)"
      >
        <uni-icons type="search" size="16" color="#999"></uni-icons>
        <text class="suggestion-text">{{ item }}</text>
      </view>
    </view>
    
    <!-- 搜索历史 -->
    <view class="search-history" v-if="!keyword && searchHistory.length > 0">
      <view class="history-header">
        <text class="history-title">搜索历史</text>
        <text class="clear-history" @click="clearHistory">清空</text>
      </view>
      <view class="history-tags">
        <view 
          class="history-tag" 
          v-for="(item, index) in searchHistory" 
          :key="index"
          @click="selectHistory(item)"
        >
          <text class="tag-text">{{ item }}</text>
          <uni-icons type="close" size="14" color="#999" @click.stop="removeHistory(index)"></uni-icons>
        </view>
      </view>
    </view>
    
    <!-- 热门搜索 -->
    <view class="hot-search" v-if="!keyword">
      <view class="hot-header">
        <text class="hot-title">热门搜索</text>
      </view>
      <view class="hot-tags">
        <view 
          class="hot-tag" 
          v-for="(item, index) in hotKeywords" 
          :key="index"
          @click="selectHot(item)"
        >
          <text class="tag-text">{{ item }}</text>
        </view>
      </view>
    </view>
    
    <!-- 搜索结果 -->
    <view class="search-results" v-if="showResults">
      <!-- 筛选栏 -->
      <view class="filter-bar">
        <view class="filter-item" @click="showLocationFilter">
          <text class="filter-text">{{ selectedLocation || '位置' }}</text>
          <uni-icons type="arrowdown" size="12" color="#666"></uni-icons>
        </view>
        <view class="filter-item" @click="showPriceFilter">
          <text class="filter-text">{{ selectedPrice.label || '价格' }}</text>
          <uni-icons type="arrowdown" size="12" color="#666"></uni-icons>
        </view>
        <view class="filter-item" @click="showTypeFilter">
          <text class="filter-text">{{ selectedType.label || '类型' }}</text>
          <uni-icons type="arrowdown" size="12" color="#666"></uni-icons>
        </view>
        <view class="filter-item" @click="showMoreFilter">
          <text class="filter-text">筛选</text>
          <uni-icons type="tune" size="14" color="#666"></uni-icons>
        </view>
      </view>
      
      <!-- 结果列表 -->
      <scroll-view 
        class="result-list" 
        scroll-y 
        @scrolltolower="loadMore"
        refresher-enabled
        @refresherrefresh="onRefresh"
        :refresher-triggered="refreshing"
      >
        <view class="result-header" v-if="searchResults.length > 0">
          <text class="result-count">找到 {{ totalCount }} 套房源</text>
          <view class="sort-btn" @click="showSortFilter">
            <text class="sort-text">{{ selectedSort.label }}</text>
            <uni-icons type="arrowdown" size="12" color="#666"></uni-icons>
          </view>
        </view>
        
        <view class="house-item" v-for="house in searchResults" :key="house._id" @click="toHouseDetail(house._id)">
          <view class="house-image">
            <image 
              :src="house.images && house.images[0] || '/static/default-house.png'" 
              mode="aspectFill"
            ></image>
            <view class="house-type">{{ getTypeText(house.type) }}</view>
          </view>
          <view class="house-info">
            <text class="house-title">{{ house.title }}</text>
            <view class="house-tags">
              <text class="tag" v-if="house.room_count">{{ house.room_count }}室</text>
              <text class="tag" v-if="house.hall_count">{{ house.hall_count }}厅</text>
              <text class="tag" v-if="house.area">{{ house.area }}㎡</text>
            </view>
            <view class="house-location">
              <uni-icons type="location" size="12" color="#999"></uni-icons>
              <text class="location-text">{{ house.location.district }} {{ house.location.address }}</text>
            </view>
            <view class="house-bottom">
              <view class="price-info">
                <text class="price">¥{{ house.price }}</text>
                <text class="price-unit">/月</text>
              </view>
              <view class="house-stats">
                <text class="stat-item">
                  <uni-icons type="eye" size="12" color="#999"></uni-icons>
                  {{ house.view_count || 0 }}
                </text>
                <text class="stat-item">
                  <uni-icons type="heart" size="12" color="#999"></uni-icons>
                  {{ house.favorite_count || 0 }}
                </text>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 加载状态 -->
        <view class="load-status" v-if="searchResults.length > 0">
          <text v-if="loading">加载中...</text>
          <text v-else-if="noMore">没有更多了</text>
        </view>
        
        <!-- 空状态 -->
        <view class="empty-state" v-if="!loading && searchResults.length === 0 && hasSearched">
          <image src="/static/empty-search.png" mode="aspectFit"></image>
          <text class="empty-text">没有找到相关房源</text>
          <text class="empty-tip">试试其他关键词或调整筛选条件</text>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script>
import request from '@/utils/request.js'
import { debounce } from '@/utils/common.js'
import { HOUSE_TYPES, PRICE_RANGES, SORT_OPTIONS } from '@/common/config.js'

export default {
  data() {
    return {
      keyword: '',
      showSuggestions: false,
      suggestions: [],
      searchHistory: [],
      hotKeywords: ['整租', '合租', '单间', '地铁附近', '精装修', '拎包入住'],
      statusBarHeight: 0, // 状态栏高度
      
      // 搜索结果
      showResults: false,
      searchResults: [],
      totalCount: 0,
      loading: false,
      refreshing: false,
      noMore: false,
      hasSearched: false,
      page: 1,
      pageSize: 10,
      
      // 筛选条件
      selectedLocation: '',
      selectedPrice: {},
      selectedType: {},
      selectedSort: { value: 'publish_date_desc', label: '最新发布' },
      
      // 筛选选项
      priceOptions: PRICE_RANGES,
      typeOptions: HOUSE_TYPES,
      sortOptions: SORT_OPTIONS
    }
  },
  methods: {
    // 输入处理（防抖）
    onInput: debounce(function() {
      if (this.keyword.trim()) {
        this.getSuggestions()
        this.showSuggestions = true
      } else {
        this.showSuggestions = false
        this.suggestions = []
      }
    }, 300),
    
    // 获取搜索建议
    async getSuggestions() {
      // 这里可以调用API获取搜索建议
      // 暂时使用模拟数据
      const mockSuggestions = [
        this.keyword + ' 整租',
        this.keyword + ' 合租',
        this.keyword + ' 附近',
        this.keyword + ' 地铁'
      ]
      this.suggestions = mockSuggestions.slice(0, 5)
    },
    
    // 执行搜索
    async handleSearch() {
      if (!this.keyword.trim()) return
      
      this.showSuggestions = false
      this.showResults = true
      this.hasSearched = true
      
      // 保存搜索历史
      this.saveSearchHistory(this.keyword.trim())
      
      // 重置搜索结果
      this.searchResults = []
      this.page = 1
      this.noMore = false
      
      await this.loadSearchResults()
    },
    
    // 加载搜索结果
    async loadSearchResults(loadMore = false) {
      if (this.loading) return
      
      this.loading = true
      
      try {
        const params = {
          action: 'searchHouses',
          data: {
            keyword: this.keyword.trim(),
            page: this.page,
            pageSize: this.pageSize
          }
        }
        
        // 添加筛选条件
        if (this.selectedType.value) {
          params.data.type = this.selectedType.value
        }
        
        if (this.selectedPrice.min !== undefined) {
          params.data.minPrice = this.selectedPrice.min
        }
        if (this.selectedPrice.max !== undefined) {
          params.data.maxPrice = this.selectedPrice.max
        }
        
        if (this.selectedSort.value) {
          params.data.sort = this.selectedSort.value
        }
        
        const result = await request.callFunction('house-management', params)
        
        if (result.code === 0) {
          const { list, total } = result.data
          
          if (loadMore) {
            this.searchResults.push(...list)
          } else {
            this.searchResults = list
          }
          
          this.totalCount = total
          this.page++
          this.noMore = this.searchResults.length >= total
        }
      } catch (error) {
        console.error('搜索失败:', error)
      } finally {
        this.loading = false
        this.refreshing = false
      }
    },
    
    // 获取房源类型文本
    getTypeText(type) {
      const typeItem = HOUSE_TYPES.find(item => item.value === type)
      return typeItem ? typeItem.label : type
    },
    
    // 选择搜索建议
    selectSuggestion(suggestion) {
      this.keyword = suggestion
      this.handleSearch()
    },
    
    // 选择搜索历史
    selectHistory(history) {
      this.keyword = history
      this.handleSearch()
    },
    
    // 选择热门搜索
    selectHot(hot) {
      this.keyword = hot
      this.handleSearch()
    },
    
    // 清空关键词
    clearKeyword() {
      this.keyword = ''
      this.showSuggestions = false
      this.showResults = false
    },
    
    // 保存搜索历史
    saveSearchHistory(keyword) {
      let history = uni.getStorageSync('searchHistory') || []
      
      // 移除重复项
      history = history.filter(item => item !== keyword)
      
      // 添加到开头
      history.unshift(keyword)
      
      // 限制数量
      if (history.length > 10) {
        history = history.slice(0, 10)
      }
      
      this.searchHistory = history
      uni.setStorageSync('searchHistory', history)
    },
    
    // 移除搜索历史
    removeHistory(index) {
      this.searchHistory.splice(index, 1)
      uni.setStorageSync('searchHistory', this.searchHistory)
    },
    
    // 清空搜索历史
    clearHistory() {
      uni.showModal({
        title: '提示',
        content: '确定要清空搜索历史吗？',
        success: (res) => {
          if (res.confirm) {
            this.searchHistory = []
            uni.removeStorageSync('searchHistory')
          }
        }
      })
    },
    
    // 加载更多
    loadMore() {
      if (!this.noMore && !this.loading) {
        this.loadSearchResults(true)
      }
    },
    
    // 下拉刷新
    onRefresh() {
      this.refreshing = true
      this.page = 1
      this.noMore = false
      this.loadSearchResults()
    },
    
    // 显示位置筛选
    showLocationFilter() {
      uni.showToast({
        title: '功能开发中',
        icon: 'none'
      })
    },
    
    // 显示价格筛选
    showPriceFilter() {
      uni.showToast({
        title: '功能开发中',
        icon: 'none'
      })
    },
    
    // 显示类型筛选
    showTypeFilter() {
      uni.showToast({
        title: '功能开发中',
        icon: 'none'
      })
    },
    
    // 显示更多筛选
    showMoreFilter() {
      uni.showToast({
        title: '功能开发中',
        icon: 'none'
      })
    },
    
    // 显示排序筛选
    showSortFilter() {
      uni.showToast({
        title: '功能开发中',
        icon: 'none'
      })
    },
    
    // 跳转到房源详情
    toHouseDetail(houseId) {
      uni.navigateTo({
        url: `/pages/house/detail?id=${houseId}`
      })
    },
    
    // 返回
    goBack() {
      uni.navigateBack()
    }
  },
  
  onLoad() {
    // 获取系统信息，包括状态栏高度
    const systemInfo = uni.getSystemInfoSync()
    this.statusBarHeight = systemInfo.statusBarHeight || 44 // 默认44px

    // 加载搜索历史
    this.searchHistory = uni.getStorageSync('searchHistory') || []
  }
}
</script>

<style scoped>
.search-container {
  height: 100vh;
  background: #f5f7fa;
  display: flex;
  flex-direction: column;
}

.search-bar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx 30rpx;
  padding-top: 150rpx; /* 默认间距，会被动态样式覆盖 */
  padding-bottom: 30rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);
}

.search-input-wrapper {
  flex: 1;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 30rpx;
  padding: 0 30rpx;
  display: flex;
  align-items: center;
  height: 80rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}

.search-input-wrapper:focus-within {
  background: #fff;
  box-shadow: 0 6rpx 30rpx rgba(0, 0, 0, 0.15);
  transform: translateY(-2rpx);
}

.search-input {
  flex: 1;
  margin-left: 15rpx;
  font-size: 28rpx;
  color: #333;
  background: transparent;
  border: none;
  outline: none;
}

.search-input::placeholder {
  color: #999;
}

.clear-btn {
  padding: 10rpx;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.clear-btn:active {
  background: rgba(0, 0, 0, 0.1);
  transform: scale(0.9);
}

.cancel-btn {
  font-size: 28rpx;
  color: #fff;
  font-weight: 500;
  padding: 16rpx 20rpx;
  border-radius: 20rpx;
  transition: all 0.3s ease;
}

.cancel-btn:active {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(0.95);
}

.suggestions {
  background: #fff;
  margin: 0 30rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.suggestion-item {
  display: flex;
  align-items: center;
  padding: 25rpx 30rpx;
  border-bottom: 1rpx solid #f8f9fa;
  transition: all 0.3s ease;
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-item:active {
  background: #f8f9fa;
}

.suggestion-text {
  font-size: 28rpx;
  color: #333;
  margin-left: 20rpx;
}

.search-history, .hot-search {
  background: #fff;
  margin: 20rpx 30rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}

.history-header, .hot-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.history-title, .hot-title {
  font-size: 30rpx;
  color: #303133;
  font-weight: 600;
}

.clear-history {
  font-size: 26rpx;
  color: #909399;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.clear-history:active {
  background: #f5f7fa;
  color: #606266;
}

.history-tags, .hot-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.history-tag, .hot-tag {
  background: #f8f9fa;
  border: 1rpx solid transparent;
  border-radius: 20rpx;
  padding: 16rpx 24rpx;
  display: flex;
  align-items: center;
  gap: 10rpx;
  transition: all 0.3s ease;
}

.history-tag:active, .hot-tag:active {
  background: #e7f4ff;
  border-color: #007aff;
  transform: scale(0.95);
}

.tag-text {
  font-size: 26rpx;
  color: #666;
}

.search-results {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.filter-bar {
  background: #fff;
  padding: 20rpx;
  display: flex;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 10rpx;
  gap: 8rpx;
}

.filter-text {
  font-size: 28rpx;
  color: #333;
}

.result-list {
  flex: 1;
  padding: 20rpx;
}

.result-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.result-count {
  font-size: 26rpx;
  color: #666;
}

.sort-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.sort-text {
  font-size: 26rpx;
  color: #666;
}

.house-item {
  background: #fff;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  display: flex;
}

.house-image {
  position: relative;
  width: 240rpx;
  height: 180rpx;
  flex-shrink: 0;
}

.house-image image {
  width: 100%;
  height: 100%;
}

.house-type {
  position: absolute;
  top: 10rpx;
  left: 10rpx;
  background: rgba(0, 122, 255, 0.9);
  color: #fff;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
}

.house-info {
  flex: 1;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.house-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.house-tags {
  display: flex;
  gap: 8rpx;
  margin-bottom: 10rpx;
}

.tag {
  background: #f0f0f0;
  color: #666;
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
  font-size: 20rpx;
}

.house-location {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.location-text {
  font-size: 24rpx;
  color: #999;
  margin-left: 6rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.house-bottom {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.price-info {
  display: flex;
  align-items: baseline;
}

.price {
  font-size: 30rpx;
  font-weight: bold;
  color: #ff6b6b;
}

.price-unit {
  font-size: 20rpx;
  color: #999;
  margin-left: 4rpx;
}

.house-stats {
  display: flex;
  gap: 15rpx;
}

.stat-item {
  font-size: 20rpx;
  color: #999;
  display: flex;
  align-items: center;
  gap: 4rpx;
}

.load-status {
  text-align: center;
  padding: 40rpx;
  color: #999;
  font-size: 28rpx;
}

.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
}

.empty-state image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
}

.empty-text {
  display: block;
  font-size: 28rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.empty-tip {
  display: block;
  font-size: 24rpx;
  color: #ccc;
}
</style>
