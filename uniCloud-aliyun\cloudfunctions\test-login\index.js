'use strict';

exports.main = async (event, context) => {
  console.log('测试登录功能...');
  
  try {
    const db = uniCloud.database();
    
    // 1. 检查用户是否存在
    console.log('检查admin用户...');
    const adminUser = await db.collection('uni-id-users').where({
      username: 'admin'
    }).get();
    
    if (adminUser.data.length === 0) {
      return {
        code: -1,
        message: 'admin用户不存在',
        suggestion: '请先运行 simple-create-admin 云函数'
      };
    }
    
    const user = adminUser.data[0];
    console.log('找到admin用户:', {
      id: user._id,
      username: user.username,
      nickname: user.nickname,
      status: user.status,
      role: user.role
    });
    
    // 2. 尝试使用uni-id-co登录
    try {
      console.log('尝试uni-id-co登录...');
      const uniIdCo = uniCloud.importObject('uni-id-co');
      
      const loginResult = await uniIdCo.login({
        username: 'admin',
        password: 'admin123456'
      });
      
      console.log('uni-id-co登录结果:', loginResult);
      
      if (loginResult.errCode === 0) {
        return {
          code: 0,
          message: 'uni-id-co登录成功',
          data: {
            method: 'uni-id-co',
            result: loginResult
          }
        };
      } else {
        console.log('uni-id-co登录失败:', loginResult.errMsg);
      }
      
    } catch (uniIdError) {
      console.log('uni-id-co登录异常:', uniIdError.message);
    }
    
    // 3. 尝试直接密码验证
    try {
      console.log('尝试直接密码验证...');
      const bcrypt = require('bcrypt');
      
      const isPasswordValid = await bcrypt.compare('admin123456', user.password);
      console.log('密码验证结果:', isPasswordValid);
      
      if (isPasswordValid) {
        return {
          code: 0,
          message: '密码验证成功',
          data: {
            method: 'direct-password-check',
            user: {
              id: user._id,
              username: user.username,
              nickname: user.nickname,
              role: user.role
            }
          }
        };
      } else {
        return {
          code: -1,
          message: '密码验证失败',
          data: {
            storedPassword: user.password,
            note: '密码hash不匹配'
          }
        };
      }
      
    } catch (bcryptError) {
      console.log('bcrypt验证异常:', bcryptError.message);
      
      // 4. 如果bcrypt失败，尝试明文密码（临时测试）
      if (user.password === 'admin123456') {
        return {
          code: 0,
          message: '明文密码匹配',
          data: {
            method: 'plain-text',
            note: '密码是明文存储的'
          }
        };
      }
    }
    
    return {
      code: -1,
      message: '所有登录方法都失败',
      data: {
        userExists: true,
        userDetails: user
      }
    };
    
  } catch (error) {
    console.error('测试登录失败:', error);
    return {
      code: -1,
      message: '测试失败',
      error: error.message
    };
  }
};
