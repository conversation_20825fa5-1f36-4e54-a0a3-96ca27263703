// 房源相关API
import { callAPI } from './index.js'

/**
 * 获取房源列表
 * @param {Object} params 查询参数
 * @param {number} params.page 页码
 * @param {number} params.pageSize 每页数量
 * @param {string} params.city 城市
 * @param {string} params.district 区县
 * @param {string} params.type 房源类型
 * @param {number} params.minPrice 最低价格
 * @param {number} params.maxPrice 最高价格
 * @param {string} params.sort 排序方式
 */
export function getHouseList(params = {}) {
  return callAPI('house-management', 'getHouseList', params, false)
}

/**
 * 获取房源详情
 * @param {string} houseId 房源ID
 */
export function getHouseDetail(houseId) {
  return callAPI('house-management', 'getHouseDetail', { houseId }, false)
}

/**
 * 搜索房源
 * @param {Object} params 搜索参数
 * @param {string} params.keyword 关键词
 * @param {number} params.page 页码
 * @param {number} params.pageSize 每页数量
 * @param {string} params.city 城市
 * @param {string} params.type 房源类型
 * @param {number} params.minPrice 最低价格
 * @param {number} params.maxPrice 最高价格
 */
export function searchHouses(params) {
  return callAPI('house-management', 'searchHouses', params, false)
}

/**
 * 发布房源
 * @param {Object} houseData 房源数据
 */
export function publishHouse(houseData) {
  return callAPI('house-management', 'publishHouse', houseData)
}

/**
 * 更新房源
 * @param {string} houseId 房源ID
 * @param {Object} houseData 房源数据
 */
export function updateHouse(houseId, houseData) {
  return callAPI('house-management', 'updateHouse', { houseId, ...houseData })
}

/**
 * 删除房源
 * @param {string} houseId 房源ID
 */
export function deleteHouse(houseId) {
  return callAPI('house-management', 'deleteHouse', { houseId })
}

/**
 * 获取我的房源
 * @param {Object} params 查询参数
 * @param {number} params.page 页码
 * @param {number} params.pageSize 每页数量
 * @param {string} params.status 状态筛选
 */
export function getMyHouses(params = {}) {
  return callAPI('house-management', 'getMyHouses', params)
}

/**
 * 更新房源状态
 * @param {string} houseId 房源ID
 * @param {string} status 新状态
 */
export function updateHouseStatus(houseId, status) {
  return callAPI('house-management', 'updateHouseStatus', { houseId, status })
}

/**
 * 增加房源浏览次数
 * @param {string} houseId 房源ID
 */
export function increaseViewCount(houseId) {
  return callAPI('house-management', 'increaseViewCount', { houseId }, false)
}

/**
 * 获取推荐房源
 * @param {number} limit 数量限制
 */
export function getRecommendedHouses(limit = 10) {
  return callAPI('house-management', 'getRecommendedHouses', { limit }, false)
}

/**
 * 获取热门房源
 * @param {number} limit 数量限制
 */
export function getPopularHouses(limit = 10) {
  return callAPI('house-management', 'getPopularHouses', { limit }, false)
}

/**
 * 获取附近房源
 * @param {Object} location 位置信息
 * @param {number} location.longitude 经度
 * @param {number} location.latitude 纬度
 * @param {number} radius 搜索半径（公里）
 * @param {number} limit 数量限制
 */
export function getNearbyHouses(location, radius = 5, limit = 20) {
  return callAPI('house-management', 'getNearbyHouses', {
    longitude: location.longitude,
    latitude: location.latitude,
    radius,
    limit
  }, false)
}
