<template>
  <view class="test-container">
    <view class="test-header">
      <text class="test-title">房源筛选功能测试</text>
      <text class="test-subtitle">测试从首页跳转到房源列表的筛选功能</text>
    </view>
    
    <view class="test-content">
      <!-- 功能说明 -->
      <view class="info-section">
        <view class="info-title">🎯 测试目标</view>
        <text class="info-text">验证点击首页快捷入口能正确跳转到房源列表并应用对应的筛选条件</text>
      </view>
      
      <!-- 测试步骤 -->
      <view class="steps-section">
        <view class="steps-title">📋 测试步骤</view>
        
        <view class="step-item">
          <view class="step-number">1</view>
          <view class="step-content">
            <text class="step-title">点击快捷入口</text>
            <text class="step-desc">点击下方的房源类型按钮</text>
          </view>
        </view>
        
        <view class="step-item">
          <view class="step-number">2</view>
          <view class="step-content">
            <text class="step-title">检查跳转</text>
            <text class="step-desc">应该跳转到房源列表页面</text>
          </view>
        </view>
        
        <view class="step-item">
          <view class="step-number">3</view>
          <view class="step-content">
            <text class="step-title">验证筛选</text>
            <text class="step-desc">房源类型应该自动选中对应类型</text>
          </view>
        </view>
      </view>
      
      <!-- 测试按钮 -->
      <view class="test-buttons">
        <view class="buttons-title">🧪 快捷入口测试</view>
        
        <button class="test-btn whole" @click="testWholeRent">
          <view class="btn-icon">🏠</view>
          <view class="btn-content">
            <text class="btn-title">整租</text>
            <text class="btn-desc">独立空间</text>
          </view>
        </button>
        
        <button class="test-btn shared" @click="testSharedRent">
          <view class="btn-icon">👥</view>
          <view class="btn-content">
            <text class="btn-title">合租</text>
            <text class="btn-desc">性价比高</text>
          </view>
        </button>
        
        <button class="test-btn single" @click="testSingleRoom">
          <view class="btn-icon">🛏️</view>
          <view class="btn-content">
            <text class="btn-title">单间</text>
            <text class="btn-desc">经济实惠</text>
          </view>
        </button>
        
        <button class="test-btn all" @click="testAllTypes">
          <view class="btn-icon">📋</view>
          <view class="btn-content">
            <text class="btn-title">全部房源</text>
            <text class="btn-desc">查看所有</text>
          </view>
        </button>
      </view>
      
      <!-- 技术说明 -->
      <view class="tech-section">
        <view class="tech-title">⚙️ 技术实现</view>
        
        <view class="tech-item">
          <text class="tech-label">参数传递</text>
          <text class="tech-value">uni.setStorageSync('houseListFilter', { type })</text>
        </view>
        
        <view class="tech-item">
          <text class="tech-label">页面跳转</text>
          <text class="tech-value">uni.switchTab({ url: '/pages/house/list' })</text>
        </view>
        
        <view class="tech-item">
          <text class="tech-label">参数接收</text>
          <text class="tech-value">onShow() 中读取 houseListFilter</text>
        </view>
        
        <view class="tech-item">
          <text class="tech-label">筛选应用</text>
          <text class="tech-value">applyTypeFilter(typeValue)</text>
        </view>
      </view>
      
      <!-- 预期结果 -->
      <view class="result-section">
        <view class="result-title">✅ 预期结果</view>
        
        <view class="result-list">
          <text class="result-item">• 点击"整租"应该显示 selectedType = "整租"</text>
          <text class="result-item">• 点击"合租"应该显示 selectedType = "合租"</text>
          <text class="result-item">• 点击"单间"应该显示 selectedType = "单间"</text>
          <text class="result-item">• 房源列表应该只显示对应类型的房源</text>
          <text class="result-item">• 筛选条件应该在页面顶部正确显示</text>
        </view>
      </view>
      
      <!-- 返回按钮 -->
      <view class="back-section">
        <button class="back-btn" @click="goBack">
          返回
        </button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  methods: {
    // 测试整租
    testWholeRent() {
      console.log('测试整租筛选')
      uni.setStorageSync('houseListFilter', { type: 'whole' })
      uni.showToast({
        title: '跳转到整租房源',
        icon: 'none'
      })
      setTimeout(() => {
        uni.switchTab({
          url: '/pages/house/list'
        })
      }, 1000)
    },
    
    // 测试合租
    testSharedRent() {
      console.log('测试合租筛选')
      uni.setStorageSync('houseListFilter', { type: 'shared' })
      uni.showToast({
        title: '跳转到合租房源',
        icon: 'none'
      })
      setTimeout(() => {
        uni.switchTab({
          url: '/pages/house/list'
        })
      }, 1000)
    },
    
    // 测试单间
    testSingleRoom() {
      console.log('测试单间筛选')
      uni.setStorageSync('houseListFilter', { type: 'single' })
      uni.showToast({
        title: '跳转到单间房源',
        icon: 'none'
      })
      setTimeout(() => {
        uni.switchTab({
          url: '/pages/house/list'
        })
      }, 1000)
    },
    
    // 测试全部类型
    testAllTypes() {
      console.log('测试全部房源')
      uni.removeStorageSync('houseListFilter')
      uni.showToast({
        title: '跳转到全部房源',
        icon: 'none'
      })
      setTimeout(() => {
        uni.switchTab({
          url: '/pages/house/list'
        })
      }, 1000)
    },
    
    // 返回
    goBack() {
      uni.navigateBack()
    }
  }
}
</script>

<style scoped>
.test-container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  padding: 40rpx;
}

.test-header {
  text-align: center;
  margin-bottom: 60rpx;
  padding-top: 60rpx;
}

.test-title {
  display: block;
  font-size: 40rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 20rpx;
}

.test-subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.test-content {
  background: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  gap: 40rpx;
}

.info-section, .steps-section, .test-buttons, .tech-section, .result-section, .back-section {
  display: flex;
  flex-direction: column;
}

.info-title, .steps-title, .buttons-title, .tech-title, .result-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #303133;
  margin-bottom: 20rpx;
}

.info-text {
  font-size: 26rpx;
  color: #606266;
  line-height: 1.6;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.step-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.step-number {
  width: 40rpx;
  height: 40rpx;
  background: #007aff;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 600;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-title {
  display: block;
  font-size: 26rpx;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8rpx;
}

.step-desc {
  display: block;
  font-size: 24rpx;
  color: #909399;
  line-height: 1.4;
}

.test-btn {
  width: 100%;
  background: #fff;
  border: 2rpx solid #e4e7ed;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
}

.test-btn:active {
  transform: scale(0.98);
  border-color: #007aff;
  background: #f0f9ff;
}

.test-btn.whole {
  border-color: #667eea;
}

.test-btn.shared {
  border-color: #f093fb;
}

.test-btn.single {
  border-color: #4facfe;
}

.test-btn.all {
  border-color: #43e97b;
}

.btn-icon {
  font-size: 40rpx;
  margin-right: 24rpx;
}

.btn-content {
  flex: 1;
  text-align: left;
}

.btn-title {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8rpx;
}

.btn-desc {
  display: block;
  font-size: 24rpx;
  color: #909399;
}

.tech-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  margin-bottom: 12rpx;
  border-left: 4rpx solid #007aff;
}

.tech-label {
  font-size: 26rpx;
  font-weight: 600;
  color: #303133;
}

.tech-value {
  font-size: 22rpx;
  color: #007aff;
  font-family: 'Monaco', 'Menlo', monospace;
  text-align: right;
  flex: 1;
  margin-left: 20rpx;
}

.result-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.result-item {
  font-size: 24rpx;
  color: #303133;
  line-height: 1.6;
  padding: 12rpx 16rpx;
  background: #f0fdf4;
  border-radius: 8rpx;
  border-left: 4rpx solid #16a34a;
}

.back-btn {
  width: 100%;
  height: 88rpx;
  background: #f5f7fa;
  color: #606266;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
}

.back-btn:active {
  opacity: 0.8;
}
</style>
