// 用户相关API
import { callAPI } from './index.js'

/**
 * 用户注册
 * @param {Object} data 注册数据
 * @param {string} data.username 用户名
 * @param {string} data.password 密码
 * @param {string} data.mobile 手机号（可选）
 * @param {string} data.email 邮箱（可选）
 * @param {string} data.nickname 昵称（可选）
 */
export function register(data) {
  return callAPI('user-auth', 'register', data, false)
}

/**
 * 用户登录
 * @param {Object} data 登录数据
 * @param {string} data.username 用户名
 * @param {string} data.password 密码
 */
export function login(data) {
  return callAPI('user-auth', 'login', data, false)
}

/**
 * 用户登出
 */
export function logout() {
  return callAPI('user-auth', 'logout', {})
}

/**
 * 获取用户信息
 */
export function getUserInfo() {
  return callAPI('user-auth', 'getUserInfo', {})
}

/**
 * 更新用户信息
 * @param {Object} data 更新数据
 * @param {string} data.nickname 昵称（可选）
 * @param {string} data.avatar 头像（可选）
 * @param {number} data.gender 性别（可选）
 */
export function updateUserInfo(data) {
  return callAPI('user-auth', 'updateProfile', data)
}

/**
 * 检查token有效性
 */
export function checkToken() {
  return callAPI('user-auth', 'checkToken', {})
}

/**
 * 手机号登录
 * @param {Object} data 登录数据
 * @param {string} data.mobile 手机号
 * @param {string} data.code 验证码
 */
export function loginByMobile(data) {
  return callAPI('user-auth', 'loginByMobile', data, false)
}

/**
 * 发送验证码
 * @param {Object} data 
 * @param {string} data.mobile 手机号
 * @param {string} data.type 验证码类型：register|login|reset
 */
export function sendSmsCode(data) {
  return callAPI('user-auth', 'sendSmsCode', data, false)
}

/**
 * 重置密码
 * @param {Object} data 
 * @param {string} data.mobile 手机号
 * @param {string} data.code 验证码
 * @param {string} data.newPassword 新密码
 */
export function resetPassword(data) {
  return callAPI('user-auth', 'resetPassword', data, false)
}

/**
 * 修改密码
 * @param {Object} data 
 * @param {string} data.oldPassword 旧密码
 * @param {string} data.newPassword 新密码
 */
export function changePassword(data) {
  return callAPI('user-auth', 'changePassword', data)
}
