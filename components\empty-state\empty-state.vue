<template>
  <view class="empty-state">
    <image :src="image" mode="aspectFit" class="empty-image"></image>
    <text class="empty-title">{{ title }}</text>
    <text class="empty-desc" v-if="desc">{{ desc }}</text>
    <button class="empty-btn" v-if="buttonText" @click="onButtonClick">{{ buttonText }}</button>
  </view>
</template>

<script>
export default {
  name: 'EmptyState',
  props: {
    image: {
      type: String,
      default: '/static/empty-default.png'
    },
    title: {
      type: String,
      default: '暂无数据'
    },
    desc: {
      type: String,
      default: ''
    },
    buttonText: {
      type: String,
      default: ''
    }
  },
  methods: {
    onButtonClick() {
      this.$emit('button-click')
    }
  }
}
</script>

<style scoped>
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  text-align: center;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
  opacity: 0.6;
}

.empty-title {
  font-size: 32rpx;
  color: #999;
  margin-bottom: 20rpx;
  font-weight: 500;
}

.empty-desc {
  font-size: 26rpx;
  color: #ccc;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

.empty-btn {
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 25rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}
</style>
