<template>
  <view class="detail-container" v-if="houseInfo._id">
    <!-- 图片轮播 -->
    <swiper class="house-swiper" indicator-dots indicator-color="rgba(255,255,255,0.5)" indicator-active-color="#fff">
      <swiper-item v-for="(image, index) in houseInfo.images" :key="index">
        <image :src="image" mode="aspectFill" @click="previewImage(index)"></image>
      </swiper-item>
      <swiper-item v-if="!houseInfo.images || houseInfo.images.length === 0">
        <image src="/static/default-house.png" mode="aspectFill"></image>
      </swiper-item>
    </swiper>
    
    <!-- 基本信息 -->
    <view class="info-section">
      <view class="price-info">
        <text class="price">¥{{ houseInfo.price }}</text>
        <text class="price-unit">/月</text>
        <text class="deposit" v-if="houseInfo.deposit">押金: ¥{{ houseInfo.deposit }}</text>
      </view>
      <text class="title">{{ houseInfo.title }}</text>
      <view class="tags">
        <text class="tag type">{{ getTypeText(houseInfo.type) }}</text>
        <text class="tag" v-if="houseInfo.room_count">{{ houseInfo.room_count }}室</text>
        <text class="tag" v-if="houseInfo.hall_count">{{ houseInfo.hall_count }}厅</text>
        <text class="tag" v-if="houseInfo.bathroom_count">{{ houseInfo.bathroom_count }}卫</text>
        <text class="tag" v-if="houseInfo.area">{{ houseInfo.area }}㎡</text>
      </view>
      <view class="location">
        <uni-icons type="location" size="14" color="#999"></uni-icons>
        <text class="location-text">{{ houseInfo.location.district }} {{ houseInfo.location.address }}</text>
      </view>
    </view>
    
    <!-- 房屋详情 -->
    <view class="detail-section">
      <view class="section-title">房屋详情</view>
      <view class="detail-grid">
        <view class="detail-item" v-if="houseInfo.orientation">
          <text class="detail-label">朝向</text>
          <text class="detail-value">{{ houseInfo.orientation }}</text>
        </view>
        <view class="detail-item" v-if="houseInfo.floor">
          <text class="detail-label">楼层</text>
          <text class="detail-value">{{ houseInfo.floor }}/{{ houseInfo.total_floors }}层</text>
        </view>
        <view class="detail-item" v-if="houseInfo.decoration">
          <text class="detail-label">装修</text>
          <text class="detail-value">{{ houseInfo.decoration }}</text>
        </view>
      </view>
      
      <view class="description" v-if="houseInfo.description">
        <text class="desc-title">房源描述</text>
        <text class="desc-content">{{ houseInfo.description }}</text>
      </view>
    </view>
    
    <!-- 房屋设施 -->
    <view class="facilities-section" v-if="houseInfo.facilities && houseInfo.facilities.length > 0">
      <view class="section-title">房屋设施</view>
      <view class="facilities-grid">
        <view class="facility-item" v-for="facility in houseInfo.facilities" :key="facility">
          <uni-icons type="checkmarkempty" size="16" color="#10c560"></uni-icons>
          <text class="facility-text">{{ getFacilityText(facility) }}</text>
        </view>
      </view>
    </view>
    
    <!-- 发布者信息 -->
    <view class="publisher-section" v-if="houseInfo.publisher_info">
      <view class="section-title">发布者信息</view>
      <view class="publisher-info">
        <image
          class="publisher-avatar"
          :src="houseInfo.publisher_info.avatar || '/static/default-avatar.png'"
          mode="aspectFill"
        ></image>
        <view class="publisher-details">
          <text class="publisher-name">{{ houseInfo.publisher_info.nickname || '发布者' }}</text>
          <text class="publisher-verified" v-if="houseInfo.publisher_info.landlord_info && houseInfo.publisher_info.landlord_info.verified">
            <uni-icons type="checkmarkempty" size="12" color="#10c560"></uni-icons>
            已认证
          </text>
        </view>
        <button class="contact-btn" @click="contactPublisher">联系发布者</button>
      </view>
    </view>
    
    <!-- 地图位置 -->
    <view class="map-section">
      <view class="section-title">位置信息</view>
      <map 
        class="house-map"
        :latitude="houseInfo.location.latitude"
        :longitude="houseInfo.location.longitude"
        :markers="mapMarkers"
        :show-location="true"
      ></map>
      <text class="map-address">{{ houseInfo.location.address }}</text>
    </view>
    
    <!-- 底部操作栏 -->
    <view class="bottom-actions">
      <view class="action-item" @click="toggleFavorite">
        <uni-icons 
          :type="isFavorited ? 'heart-filled' : 'heart'" 
          size="24" 
          :color="isFavorited ? '#ff6b6b' : '#666'"
        ></uni-icons>
        <text class="action-text">{{ isFavorited ? '已收藏' : '收藏' }}</text>
      </view>
      <view class="action-item" @click="shareHouse">
        <uni-icons type="redo" size="24" color="#666"></uni-icons>
        <text class="action-text">分享</text>
      </view>
      <button class="appointment-btn" @click="makeAppointment">预约看房</button>
    </view>
  </view>
  
  <!-- 加载状态 -->
  <view class="loading-container" v-else-if="loading">
    <text class="loading-text">加载中...</text>
  </view>
  
  <!-- 空状态 -->
  <view class="empty-container" v-else>
    <text class="empty-text">房源不存在或已下线</text>
    <button class="back-btn" @click="goBack">返回</button>
  </view>
</template>

<script>
import request from '@/utils/request.js'
import { checkLogin, formatRelativeTime } from '@/utils/common.js'
import { HOUSE_TYPES, FACILITIES } from '@/common/config.js'

export default {
  data() {
    return {
      houseId: '',
      houseInfo: {},
      loading: true,
      isFavorited: false,
      mapMarkers: []
    }
  },
  methods: {
    // 获取房源类型文本
    getTypeText(type) {
      const typeItem = HOUSE_TYPES.find(item => item.value === type)
      return typeItem ? typeItem.label : type
    },
    
    // 获取设施文本
    getFacilityText(facility) {
      const facilityItem = FACILITIES.find(item => item.value === facility)
      return facilityItem ? facilityItem.label : facility
    },
    
    // 加载房源详情
    async loadHouseDetail() {
      this.loading = true
      
      try {
        const result = await request.callFunction('house-management', {
          action: 'getHouseDetail',
          data: {
            house_id: this.houseId
          }
        })
        
        if (result.code === 0) {
          this.houseInfo = result.data
          
          // 设置地图标记
          if (this.houseInfo.location.latitude && this.houseInfo.location.longitude) {
            this.mapMarkers = [{
              id: 1,
              latitude: this.houseInfo.location.latitude,
              longitude: this.houseInfo.location.longitude,
              title: this.houseInfo.title,
              iconPath: '/static/map-marker.png',
              width: 30,
              height: 30
            }]
          }
          
          // 检查收藏状态
          this.checkFavoriteStatus()
        }
      } catch (error) {
        console.error('加载房源详情失败:', error)
      } finally {
        this.loading = false
      }
    },
    
    // 检查收藏状态
    async checkFavoriteStatus() {
      if (!checkLogin()) return
      
      try {
        const result = await request.callFunction('favorite-management', {
          action: 'checkFavoriteStatus',
          data: {
            house_id: this.houseId
          }
        })
        
        if (result.code === 0) {
          this.isFavorited = result.data.is_favorited
        }
      } catch (error) {
        console.error('检查收藏状态失败:', error)
      }
    },
    
    // 切换收藏状态
    async toggleFavorite() {
      if (!checkLogin()) {
        uni.showToast({
          title: '请先登录',
          icon: 'none'
        })
        uni.navigateTo({
          url: '/pages/login/login'
        })
        return
      }
      
      try {
        const action = this.isFavorited ? 'removeFavorite' : 'addFavorite'
        const result = await request.callFunction('favorite-management', {
          action,
          data: {
            house_id: this.houseId
          }
        })
        
        if (result.code === 0) {
          this.isFavorited = !this.isFavorited
          uni.showToast({
            title: this.isFavorited ? '收藏成功' : '取消收藏',
            icon: 'success'
          })
        }
      } catch (error) {
        console.error('收藏操作失败:', error)
      }
    },
    
    // 预览图片
    previewImage(index) {
      uni.previewImage({
        urls: this.houseInfo.images,
        current: index
      })
    },
    
    // 联系发布者
    contactPublisher() {
      if (!this.houseInfo.contact) {
        uni.showToast({
          title: '暂无联系方式',
          icon: 'none'
        })
        return
      }
      
      uni.showActionSheet({
        itemList: ['拨打电话', '复制微信号'],
        success: (res) => {
          if (res.tapIndex === 0) {
            // 拨打电话
            uni.makePhoneCall({
              phoneNumber: this.houseInfo.contact.phone
            })
          } else if (res.tapIndex === 1) {
            // 复制微信号
            if (this.houseInfo.contact.wechat) {
              uni.setClipboardData({
                data: this.houseInfo.contact.wechat,
                success: () => {
                  uni.showToast({
                    title: '微信号已复制',
                    icon: 'success'
                  })
                }
              })
            } else {
              uni.showToast({
                title: '暂无微信号',
                icon: 'none'
              })
            }
          }
        }
      })
    },
    
    // 预约看房
    makeAppointment() {
      if (!checkLogin()) {
        uni.showToast({
          title: '请先登录',
          icon: 'none'
        })
        uni.navigateTo({
          url: '/pages/login/login'
        })
        return
      }
      
      uni.showToast({
        title: '功能开发中',
        icon: 'none'
      })
    },
    
    // 分享房源
    shareHouse() {
      uni.showToast({
        title: '功能开发中',
        icon: 'none'
      })
    },
    
    // 返回
    goBack() {
      uni.navigateBack()
    }
  },
  
  onLoad(options) {
    this.houseId = options.id
    if (this.houseId) {
      this.loadHouseDetail()
    } else {
      this.loading = false
    }
  },
  
  onShow() {
    // 从其他页面返回时重新检查收藏状态
    if (this.houseInfo._id) {
      this.checkFavoriteStatus()
    }
  }
}
</script>

<style scoped>
.detail-container {
  background: #f8f9fa;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.house-swiper {
  height: 500rpx;
}

.house-swiper image {
  width: 100%;
  height: 100%;
}

.info-section {
  background: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.price-info {
  display: flex;
  align-items: baseline;
  margin-bottom: 20rpx;
}

.price {
  font-size: 48rpx;
  font-weight: bold;
  color: #ff6b6b;
}

.price-unit {
  font-size: 28rpx;
  color: #999;
  margin-left: 8rpx;
}

.deposit {
  font-size: 24rpx;
  color: #666;
  margin-left: 30rpx;
  background: #f0f0f0;
  padding: 8rpx 12rpx;
  border-radius: 8rpx;
}

.title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  line-height: 1.4;
}

.tags {
  display: flex;
  gap: 10rpx;
  margin-bottom: 20rpx;
  flex-wrap: wrap;
}

.tag {
  background: #f0f0f0;
  color: #666;
  padding: 8rpx 12rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
}

.tag.type {
  background: #e3f2fd;
  color: #007aff;
}

.location {
  display: flex;
  align-items: center;
}

.location-text {
  font-size: 28rpx;
  color: #666;
  margin-left: 8rpx;
}

.detail-section, .facilities-section, .landlord-section, .map-section {
  background: #fff;
  margin-bottom: 20rpx;
  padding: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.detail-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 30rpx;
  margin-bottom: 30rpx;
}

.detail-item {
  flex: 1;
  min-width: 200rpx;
  text-align: center;
}

.detail-label {
  display: block;
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.detail-value {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.description {
  margin-top: 30rpx;
}

.desc-title {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 15rpx;
}

.desc-content {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

.facilities-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.facility-item {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  padding: 15rpx 20rpx;
  border-radius: 25rpx;
  min-width: 120rpx;
}

.facility-text {
  font-size: 26rpx;
  color: #333;
  margin-left: 10rpx;
}

.publisher-info {
  display: flex;
  align-items: center;
}

.publisher-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  margin-right: 20rpx;
}

.publisher-details {
  flex: 1;
}

.publisher-name {
  display: block;
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.landlord-verified {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #10c560;
}

.contact-btn {
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 25rpx;
  padding: 15rpx 30rpx;
  font-size: 26rpx;
}

.house-map {
  width: 100%;
  height: 300rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.map-address {
  display: block;
  font-size: 28rpx;
  color: #666;
  text-align: center;
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  align-items: center;
  gap: 30rpx;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.action-text {
  font-size: 24rpx;
  color: #666;
}

.appointment-btn {
  flex: 1;
  height: 80rpx;
  background: linear-gradient(45deg, #007aff, #0056d3);
  color: #fff;
  border: none;
  border-radius: 40rpx;
  font-size: 30rpx;
  font-weight: bold;
}

.loading-container, .empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  padding: 40rpx;
}

.loading-text, .empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.back-btn {
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 25rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}
</style>
