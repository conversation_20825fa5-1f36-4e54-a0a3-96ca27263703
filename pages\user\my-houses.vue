<template>
  <view class="my-houses-container">
    <!-- 状态筛选 -->
    <view class="status-tabs">
      <view
        class="tab-item"
        v-for="(tab, index) in statusTabs"
        :key="index"
        :class="{ active: currentTab === index }"
        @click="switchTab(index)"
      >
        <text class="tab-text">{{ tab.label }}</text>
        <text class="tab-count" v-if="tab.count > 0">{{ tab.count }}</text>
      </view>
    </view>
    
    <!-- 房源列表 -->
    <scroll-view 
      class="house-list" 
      scroll-y 
      @scrolltolower="loadMore"
      refresher-enabled
      @refresherrefresh="onRefresh"
      :refresher-triggered="refreshing"
    >
      <view class="house-item" v-for="house in houseList" :key="house._id">
        <view class="house-image" @click="toHouseDetail(house._id)">
          <image 
            :src="house.images && house.images[0] || '/static/default-house.png'" 
            mode="aspectFill"
          ></image>
          <view class="house-status" :class="house.status">{{ getStatusText(house.status) }}</view>
        </view>
        <view class="house-info" @click="toHouseDetail(house._id)">
          <text class="house-title">{{ house.title }}</text>
          <view class="house-tags">
            <text class="tag">{{ getTypeText(house.type) }}</text>
            <text class="tag" v-if="house.room_count">{{ house.room_count }}室</text>
            <text class="tag" v-if="house.hall_count">{{ house.hall_count }}厅</text>
            <text class="tag" v-if="house.area">{{ house.area }}㎡</text>
          </view>
          <view class="house-location">
            <uni-icons type="location" size="12" color="#999"></uni-icons>
            <text class="location-text">{{ house.location.district }} {{ house.location.address }}</text>
          </view>
          <view class="house-bottom">
            <view class="price-info">
              <text class="price">¥{{ house.price }}</text>
              <text class="price-unit">/月</text>
            </view>
            <view class="house-stats">
              <text class="stat-item">
                <uni-icons type="eye" size="12" color="#999"></uni-icons>
                {{ house.view_count || 0 }}
              </text>
              <text class="stat-item">
                <uni-icons type="heart" size="12" color="#999"></uni-icons>
                {{ house.favorite_count || 0 }}
              </text>
            </view>
          </view>
          <text class="publish-time">发布时间：{{ formatTime(house.publish_date) }}</text>
        </view>
        <view class="house-actions">
          <button class="action-btn edit" @click="editHouse(house._id)" v-if="house.status !== 'rented'">编辑</button>
          <button class="action-btn status" @click="toggleStatus(house)" v-if="house.status !== 'rented'">
            {{ house.status === 'available' ? '下线' : '上线' }}
          </button>
          <button class="action-btn delete" @click="deleteHouse(house._id)">删除</button>
        </view>
      </view>
      
      <!-- 加载状态 -->
      <view class="load-status" v-if="houseList.length > 0">
        <text v-if="loading">加载中...</text>
        <text v-else-if="noMore">没有更多了</text>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" v-if="!loading && houseList.length === 0">
        <image src="/static/empty-house.png" mode="aspectFit"></image>
        <text class="empty-text">暂无房源发布</text>
        <button class="publish-btn" @click="toPublish">立即发布</button>
      </view>
    </scroll-view>
    
    <!-- 发布按钮 -->
    <view class="floating-btn" @click="toPublish">
      <uni-icons type="plus" size="24" color="#fff"></uni-icons>
    </view>
  </view>
</template>

<script>
import request from '@/utils/request.js'
import { formatTime } from '@/utils/common.js'
import { HOUSE_TYPES, HOUSE_STATUS } from '@/common/config.js'

export default {
  data() {
    return {
      currentTab: 0,
      statusTabs: [
        { label: '全部', value: '', count: 0 },
        { label: '可租', value: 'available', count: 0 },
        { label: '已租', value: 'rented', count: 0 },
        { label: '下线', value: 'offline', count: 0 }
      ],
      houseList: [],
      loading: false,
      refreshing: false,
      noMore: false,
      page: 1,
      pageSize: 10
    }
  },
  methods: {
    formatTime,
    
    // 获取房源类型文本
    getTypeText(type) {
      const typeItem = HOUSE_TYPES.find(item => item.value === type)
      return typeItem ? typeItem.label : type
    },
    
    // 获取状态文本
    getStatusText(status) {
      return HOUSE_STATUS[status] || status
    },
    
    // 切换标签
    switchTab(index) {
      this.currentTab = index
      this.loadHouseList(true)
    },
    
    // 加载房源列表
    async loadHouseList(refresh = false) {
      if (this.loading) return
      
      this.loading = true
      
      if (refresh) {
        this.page = 1
        this.noMore = false
      }
      
      try {
        const params = {
          action: 'getMyHouses',
          data: {
            page: this.page,
            pageSize: this.pageSize
          }
        }
        
        // 添加状态筛选
        const currentStatus = this.statusTabs[this.currentTab].value
        if (currentStatus) {
          params.data.status = currentStatus
        }
        
        const result = await request.callFunction('house-management', params)

        if (result.code === 0) {
          const { list, total } = result.data

          if (refresh) {
            this.houseList = list
          } else {
            this.houseList.push(...list)
          }

          this.page++
          this.noMore = this.houseList.length >= total
        } else {
          console.error('获取房源列表失败:', result)
          uni.showToast({
            title: result.message || '获取房源列表失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('加载房源列表失败:', error)
      } finally {
        this.loading = false
        this.refreshing = false
      }
    },
    
    // 加载统计数据
    async loadStatistics() {
      try {
        // 并发请求各状态的房源数量，禁用单个请求的loading
        const promises = [
          // 全部房源
          request.callFunction('house-management', {
            action: 'getMyHouses',
            data: { page: 1, pageSize: 1, countOnly: true }
          }, { showLoading: false, showError: false }),
          // 可租房源
          request.callFunction('house-management', {
            action: 'getMyHouses',
            data: { page: 1, pageSize: 1, status: 'available', countOnly: true }
          }, { showLoading: false, showError: false }),
          // 已租房源
          request.callFunction('house-management', {
            action: 'getMyHouses',
            data: { page: 1, pageSize: 1, status: 'rented', countOnly: true }
          }, { showLoading: false, showError: false }),
          // 下线房源
          request.callFunction('house-management', {
            action: 'getMyHouses',
            data: { page: 1, pageSize: 1, status: 'offline', countOnly: true }
          }, { showLoading: false, showError: false })
        ]

        const results = await Promise.allSettled(promises)

        results.forEach((result, index) => {
          if (result.status === 'fulfilled' && result.value.code === 0) {
            this.statusTabs[index].count = result.value.data.total || 0
          } else {
            console.error(`获取状态 ${this.statusTabs[index].label} 数量失败:`, result.reason || result.value)
            this.statusTabs[index].count = 0
          }
        })
      } catch (error) {
        console.error('加载统计数据失败:', error)
        // 发生错误时重置为0
        this.statusTabs.forEach(tab => {
          tab.count = 0
        })
      }
    },
    
    // 加载更多
    loadMore() {
      if (!this.noMore && !this.loading) {
        this.loadHouseList()
      }
    },
    
    // 下拉刷新
    onRefresh() {
      this.refreshing = true
      this.loadHouseList(true)
      this.loadStatistics()
    },
    
    // 编辑房源
    editHouse(houseId) {
      uni.navigateTo({
        url: `/pages/house/edit?id=${houseId}`
      })
    },
    
    // 切换房源状态
    async toggleStatus(house) {
      const newStatus = house.status === 'available' ? 'offline' : 'available'
      const statusText = newStatus === 'available' ? '上线' : '下线'
      
      uni.showModal({
        title: '提示',
        content: `确定要${statusText}此房源吗？`,
        success: async (res) => {
          if (res.confirm) {
            try {
              const result = await request.callFunction('house-management', {
                action: 'updateHouseStatus',
                data: {
                  house_id: house._id,
                  status: newStatus
                }
              })
              
              if (result.code === 0) {
                uni.showToast({
                  title: `${statusText}成功`,
                  icon: 'success'
                })
                
                // 更新本地数据
                house.status = newStatus
                
                // 重新加载统计数据
                this.loadStatistics()
              }
            } catch (error) {
              console.error(`${statusText}失败:`, error)
            }
          }
        }
      })
    },
    
    // 删除房源
    deleteHouse(houseId) {
      uni.showModal({
        title: '提示',
        content: '确定要删除此房源吗？删除后无法恢复。',
        success: async (res) => {
          if (res.confirm) {
            try {
              const result = await request.callFunction('house-management', {
                action: 'deleteHouse',
                data: {
                  house_id: houseId
                }
              })
              
              if (result.code === 0) {
                uni.showToast({
                  title: '删除成功',
                  icon: 'success'
                })
                
                // 从列表中移除
                const index = this.houseList.findIndex(item => item._id === houseId)
                if (index > -1) {
                  this.houseList.splice(index, 1)
                }
                
                // 重新加载统计数据
                this.loadStatistics()
              }
            } catch (error) {
              console.error('删除失败:', error)
            }
          }
        }
      })
    },
    
    // 跳转到房源详情
    toHouseDetail(houseId) {
      uni.navigateTo({
        url: `/pages/house/detail?id=${houseId}`
      })
    },
    
    // 跳转到发布页
    toPublish() {
      uni.navigateTo({
        url: '/pages/house/publish'
      })
    }
  },
  
  onLoad() {
    this.loadHouseList(true)
    this.loadStatistics()
  },

  onShow() {
    // 从编辑页返回时刷新列表
    this.loadHouseList(true)
    this.loadStatistics()
  },
  
  onPullDownRefresh() {
    this.onRefresh()
    uni.stopPullDownRefresh()
  }
}
</script>

<style scoped>
.my-houses-container {
  height: 100vh;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
}

.status-tabs {
  background: #fff;
  display: flex;
  border-bottom: 1rpx solid #f0f0f0;
}

.tab-item {
  flex: 1;
  padding: 30rpx 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  position: relative;
}

.tab-item.active {
  color: #007aff;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background: #007aff;
  border-radius: 2rpx;
}

.tab-text {
  font-size: 28rpx;
  color: #333;
}

.tab-item.active .tab-text {
  color: #007aff;
  font-weight: 500;
}

.tab-count {
  background: #ff4757;
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 10rpx;
  min-width: 32rpx;
  text-align: center;
}

.house-list {
  flex: 1;
  padding: 20rpx;
}

.house-item {
  background: #fff;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  display: flex;
}

.house-image {
  position: relative;
  width: 240rpx;
  height: 200rpx;
  flex-shrink: 0;
}

.house-image image {
  width: 100%;
  height: 100%;
}

.house-status {
  position: absolute;
  top: 15rpx;
  left: 15rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  color: #fff;
}

.house-status.available {
  background: #10c560;
}

.house-status.rented {
  background: #ff6b6b;
}

.house-status.offline {
  background: #999;
}

.house-info {
  flex: 1;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-width: 0; /* 防止flex子项溢出 */
}

.house-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.house-tags {
  display: flex;
  gap: 8rpx;
  margin-bottom: 10rpx;
  flex-wrap: wrap;
}

.tag {
  background: #f0f0f0;
  color: #666;
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
  font-size: 22rpx;
}

.house-location {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
  min-width: 0; /* 防止溢出 */
}

.location-text {
  font-size: 24rpx;
  color: #999;
  margin-left: 6rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1; /* 占用剩余空间 */
  min-width: 0; /* 防止溢出 */
}

.house-bottom {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10rpx;
}

.price-info {
  display: flex;
  align-items: baseline;
}

.price {
  font-size: 30rpx;
  font-weight: bold;
  color: #ff6b6b;
}

.price-unit {
  font-size: 20rpx;
  color: #999;
  margin-left: 4rpx;
}

.house-stats {
  display: flex;
  gap: 15rpx;
}

.stat-item {
  font-size: 20rpx;
  color: #999;
  display: flex;
  align-items: center;
  gap: 4rpx;
}

.publish-time {
  font-size: 22rpx;
  color: #ccc;
}

.house-actions {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 20rpx;
  gap: 15rpx;
  border-left: 1rpx solid #f0f0f0;
  width: 160rpx; /* 固定宽度，确保按钮可见 */
  flex-shrink: 0; /* 防止被压缩 */
}

.action-btn {
  padding: 16rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  border: none;
  width: 100%; /* 占满容器宽度 */
  text-align: center;
  font-weight: 500;
  transition: all 0.3s ease;
}

.action-btn:active {
  transform: scale(0.95);
}

.action-btn.edit {
  background: #e3f2fd;
  color: #007aff;
  border: 1rpx solid #007aff;
}

.action-btn.status {
  background: #fff3e0;
  color: #ff9800;
  border: 1rpx solid #ff9800;
}

.action-btn.delete {
  background: #ffebee;
  color: #f44336;
  border: 1rpx solid #f44336;
}

.load-status {
  text-align: center;
  padding: 40rpx;
  color: #999;
  font-size: 28rpx;
}

.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
}

.empty-state image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
}

.empty-text {
  display: block;
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.publish-btn {
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 25rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

.floating-btn {
  position: fixed;
  right: 40rpx;
  bottom: 100rpx;
  width: 100rpx;
  height: 100rpx;
  background: linear-gradient(45deg, #007aff, #0056d3);
  border-radius: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 30rpx rgba(0, 122, 255, 0.3);
}
</style>
