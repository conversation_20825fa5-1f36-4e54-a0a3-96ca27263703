<template>
  <view class="register-container">
    <view class="header">
      <text class="title">注册账号</text>
      <text class="subtitle">加入毕业租房平台</text>
    </view>
    
    <view class="form-container">
      <view class="form-item">
        <text class="label">用户名</text>
        <view class="input-wrapper">
          <input 
            class="input" 
            type="text" 
            placeholder="请输入用户名" 
            v-model="form.username"
            maxlength="20"
          />
        </view>
      </view>
      
      <view class="form-item">
        <text class="label">手机号</text>
        <view class="input-wrapper">
          <input 
            class="input" 
            type="number" 
            placeholder="请输入手机号" 
            v-model="form.mobile"
            maxlength="11"
          />
        </view>
      </view>
      
      <view class="form-item">
        <text class="label">邮箱（可选）</text>
        <view class="input-wrapper">
          <input 
            class="input" 
            type="text" 
            placeholder="请输入邮箱" 
            v-model="form.email"
          />
        </view>
      </view>
      
      <view class="form-item">
        <text class="label">密码</text>
        <view class="input-wrapper">
          <input 
            class="input" 
            :type="showPassword ? 'text' : 'password'" 
            placeholder="请输入密码（6-20位）" 
            v-model="form.password"
            maxlength="20"
          />
          <uni-icons 
            :type="showPassword ? 'eye-slash' : 'eye'" 
            size="20" 
            color="#999"
            @click="togglePassword"
          ></uni-icons>
        </view>
      </view>
      
      <view class="form-item">
        <text class="label">确认密码</text>
        <view class="input-wrapper">
          <input 
            class="input" 
            :type="showConfirmPassword ? 'text' : 'password'" 
            placeholder="请再次输入密码" 
            v-model="form.confirmPassword"
            maxlength="20"
          />
          <uni-icons 
            :type="showConfirmPassword ? 'eye-slash' : 'eye'" 
            size="20" 
            color="#999"
            @click="toggleConfirmPassword"
          ></uni-icons>
        </view>
      </view>
      
      <view class="form-item">
        <text class="label">身份类型</text>
        <view class="role-selector">
          <view 
            class="role-item" 
            :class="{ active: form.role === 'student' }"
            @click="selectRole('student')"
          >
            <uni-icons type="person" size="24" :color="form.role === 'student' ? '#007aff' : '#999'"></uni-icons>
            <text class="role-text">学生</text>
          </view>
          <view 
            class="role-item" 
            :class="{ active: form.role === 'landlord' }"
            @click="selectRole('landlord')"
          >
            <uni-icons type="home" size="24" :color="form.role === 'landlord' ? '#007aff' : '#999'"></uni-icons>
            <text class="role-text">房东</text>
          </view>
        </view>
      </view>
      
      <view class="agreement">
        <checkbox-group @change="onAgreementChange">
          <label class="agreement-item">
            <checkbox value="agree" :checked="agreed" />
            <text class="agreement-text">我已阅读并同意</text>
            <text class="agreement-link" @click="toPrivacy">《用户协议》</text>
            <text class="agreement-text">和</text>
            <text class="agreement-link" @click="toPrivacy">《隐私政策》</text>
          </label>
        </checkbox-group>
      </view>
      
      <button class="register-btn" @click="handleRegister" :disabled="!canRegister">
        {{ loading ? '注册中...' : '注册' }}
      </button>
      
      <view class="links">
        <text class="link" @click="toLogin">已有账号？立即登录</text>
      </view>
    </view>
  </view>
</template>

<script>
import request from '@/utils/request.js'
import { validatePhone, validateEmail, setUserInfo } from '@/utils/common.js'

export default {
  data() {
    return {
      form: {
        username: '',
        mobile: '',
        email: '',
        password: '',
        confirmPassword: '',
        role: 'student'
      },
      showPassword: false,
      showConfirmPassword: false,
      agreed: false,
      loading: false
    }
  },
  computed: {
    canRegister() {
      const { username, mobile, password, confirmPassword } = this.form
      return username.trim() && 
             mobile.trim() && 
             password.trim() && 
             confirmPassword.trim() && 
             this.agreed && 
             !this.loading
    }
  },
  methods: {
    // 切换密码显示状态
    togglePassword() {
      this.showPassword = !this.showPassword
    },
    
    toggleConfirmPassword() {
      this.showConfirmPassword = !this.showConfirmPassword
    },
    
    // 选择身份类型
    selectRole(role) {
      this.form.role = role
    },
    
    // 协议勾选变化
    onAgreementChange(e) {
      this.agreed = e.detail.value.includes('agree')
    },
    
    // 注册处理
    async handleRegister() {
      if (!this.canRegister) return
      
      const { username, mobile, email, password, confirmPassword, role } = this.form
      
      // 表单验证
      if (username.length < 2) {
        uni.showToast({
          title: '用户名至少2个字符',
          icon: 'none'
        })
        return
      }
      
      if (!validatePhone(mobile)) {
        uni.showToast({
          title: '手机号格式不正确',
          icon: 'none'
        })
        return
      }
      
      if (email && !validateEmail(email)) {
        uni.showToast({
          title: '邮箱格式不正确',
          icon: 'none'
        })
        return
      }
      
      if (password.length < 6) {
        uni.showToast({
          title: '密码至少6个字符',
          icon: 'none'
        })
        return
      }
      
      if (password !== confirmPassword) {
        uni.showToast({
          title: '两次密码输入不一致',
          icon: 'none'
        })
        return
      }
      
      this.loading = true
      
      try {
        const result = await request.callFunction('user-auth', {
          action: 'register',
          data: {
            username: username.trim(),
            mobile: mobile.trim(),
            email: email.trim() || undefined,
            password: password,
            role: role
          }
        })
        
        if (result.code === 0) {
          uni.showToast({
            title: '注册成功',
            icon: 'success'
          })
          
          // 延迟跳转到登录页
          setTimeout(() => {
            uni.navigateBack()
          }, 1500)
        }
      } catch (error) {
        console.error('注册失败:', error)
      } finally {
        this.loading = false
      }
    },
    
    // 跳转到登录页
    toLogin() {
      uni.navigateBack()
    },
    
    // 跳转到隐私政策页
    toPrivacy() {
      uni.showToast({
        title: '功能开发中',
        icon: 'none'
      })
    }
  }
}
</script>

<style scoped>
.register-container {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 40rpx;
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
}

.title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: #666;
}

.form-container {
  background: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
}

.form-item {
  margin-bottom: 40rpx;
}

.label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: 500;
}

.input-wrapper {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 0 24rpx;
  display: flex;
  align-items: center;
  height: 88rpx;
  border: 2rpx solid transparent;
}

.input-wrapper:focus-within {
  border-color: #007aff;
  background: #fff;
}

.input {
  flex: 1;
  font-size: 32rpx;
  color: #333;
}

.role-selector {
  display: flex;
  gap: 20rpx;
}

.role-item {
  flex: 1;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 30rpx 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  border: 2rpx solid transparent;
}

.role-item.active {
  background: #e3f2fd;
  border-color: #007aff;
}

.role-text {
  font-size: 28rpx;
  color: #333;
  margin-top: 10rpx;
}

.agreement {
  margin-bottom: 40rpx;
}

.agreement-item {
  display: flex;
  align-items: center;
  font-size: 26rpx;
}

.agreement-text {
  color: #666;
  margin-left: 10rpx;
}

.agreement-link {
  color: #007aff;
  margin-left: 5rpx;
}

.register-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(45deg, #007aff, #0056d3);
  color: #fff;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
}

.register-btn:disabled {
  background: #ccc;
}

.links {
  text-align: center;
}

.link {
  color: #007aff;
  font-size: 28rpx;
}
</style>
