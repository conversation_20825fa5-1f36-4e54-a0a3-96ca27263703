<template>
  <view class="debug-container">
    <view class="debug-header">
      <text class="debug-title">间距调试工具</text>
      <text class="debug-subtitle">检查状态栏高度和间距设置</text>
    </view>
    
    <view class="debug-content">
      <!-- 系统信息 -->
      <view class="info-section">
        <view class="info-title">📱 系统信息</view>
        
        <view class="info-item">
          <text class="info-label">状态栏高度</text>
          <text class="info-value">{{ statusBarHeight }}px ({{ statusBarHeight * 2 }}rpx)</text>
        </view>
        
        <view class="info-item">
          <text class="info-label">屏幕宽度</text>
          <text class="info-value">{{ screenWidth }}px</text>
        </view>
        
        <view class="info-item">
          <text class="info-label">屏幕高度</text>
          <text class="info-value">{{ screenHeight }}px</text>
        </view>
        
        <view class="info-item">
          <text class="info-label">设备型号</text>
          <text class="info-value">{{ deviceModel }}</text>
        </view>
        
        <view class="info-item">
          <text class="info-label">系统版本</text>
          <text class="info-value">{{ systemVersion }}</text>
        </view>
      </view>
      
      <!-- 间距计算 -->
      <view class="calc-section">
        <view class="calc-title">📏 间距计算</view>
        
        <view class="calc-item">
          <text class="calc-label">状态栏高度 (rpx)</text>
          <text class="calc-value">{{ statusBarHeight * 2 }}rpx</text>
        </view>
        
        <view class="calc-item">
          <text class="calc-label">额外间距</text>
          <text class="calc-value">60rpx</text>
        </view>
        
        <view class="calc-item">
          <text class="calc-label">总顶部间距</text>
          <text class="calc-value">{{ totalPaddingTop }}rpx</text>
        </view>
        
        <view class="calc-note">
          <text class="note-text">计算公式：状态栏高度 × 2 + 60rpx = {{ totalPaddingTop }}rpx</text>
        </view>
      </view>
      
      <!-- 测试区域 -->
      <view class="test-section">
        <view class="test-title">🧪 测试区域</view>
        
        <!-- 模拟搜索栏 -->
        <view class="mock-search-bar" :style="{ paddingTop: totalPaddingTop + 'rpx' }">
          <view class="mock-input">
            <uni-icons type="search" size="18" color="#666"></uni-icons>
            <text class="mock-placeholder">模拟搜索栏 - 检查间距</text>
          </view>
          <text class="mock-cancel">取消</text>
        </view>
        
        <view class="test-note">
          <text class="note-text">上方搜索栏使用动态计算的间距：{{ totalPaddingTop }}rpx</text>
        </view>
      </view>
      
      <!-- 测试按钮 */
      <view class="test-actions">
        <view class="action-title">🔧 功能测试</view>
        
        <button class="test-btn primary" @click="testSearch">
          测试搜索页面间距
        </button>
        
        <button class="test-btn" @click="testIndex">
          测试首页间距
        </button>
        
        <button class="test-btn" @click="refreshInfo">
          刷新系统信息
        </button>
        
        <button class="test-btn" @click="goBack">
          返回
        </button>
      </view>
      
      <!-- 修复说明 -->
      <view class="fix-section">
        <view class="fix-title">✅ 修复说明</view>
        
        <view class="fix-list">
          <text class="fix-item">• 使用 uni.getSystemInfoSync() 动态获取状态栏高度</text>
          <text class="fix-item">• 通过 :style 动态设置 padding-top</text>
          <text class="fix-item">• 计算公式：状态栏高度 × 2 + 60rpx</text>
          <text class="fix-item">• 兼容不同设备的状态栏高度差异</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      statusBarHeight: 0,
      screenWidth: 0,
      screenHeight: 0,
      deviceModel: '',
      systemVersion: ''
    }
  },
  
  computed: {
    totalPaddingTop() {
      return this.statusBarHeight * 2 + 60
    }
  },
  
  onLoad() {
    this.getSystemInfo()
  },
  
  methods: {
    getSystemInfo() {
      const systemInfo = uni.getSystemInfoSync()
      this.statusBarHeight = systemInfo.statusBarHeight || 44
      this.screenWidth = systemInfo.screenWidth
      this.screenHeight = systemInfo.screenHeight
      this.deviceModel = systemInfo.model
      this.systemVersion = systemInfo.system
      
      console.log('系统信息:', systemInfo)
    },
    
    refreshInfo() {
      this.getSystemInfo()
      uni.showToast({
        title: '系统信息已刷新',
        icon: 'success'
      })
    },
    
    testSearch() {
      uni.navigateTo({
        url: '/pages/search/search'
      })
    },
    
    testIndex() {
      uni.switchTab({
        url: '/pages/index/index'
      })
    },
    
    goBack() {
      uni.navigateBack()
    }
  }
}
</script>

<style scoped>
.debug-container {
  background: #f5f7fa;
  min-height: 100vh;
  padding: 40rpx;
}

.debug-header {
  text-align: center;
  margin-bottom: 40rpx;
  padding-top: 60rpx;
}

.debug-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #303133;
  margin-bottom: 16rpx;
}

.debug-subtitle {
  display: block;
  font-size: 26rpx;
  color: #909399;
}

.debug-content {
  display: flex;
  flex-direction: column;
  gap: 40rpx;
}

.info-section, .calc-section, .test-section, .test-actions, .fix-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}

.info-title, .calc-title, .test-title, .action-title, .fix-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #303133;
  margin-bottom: 24rpx;
}

.info-item, .calc-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f2f6;
}

.info-item:last-child, .calc-item:last-child {
  border-bottom: none;
}

.info-label, .calc-label {
  font-size: 26rpx;
  color: #606266;
}

.info-value, .calc-value {
  font-size: 26rpx;
  color: #303133;
  font-weight: 500;
}

.calc-note, .test-note {
  margin-top: 20rpx;
  padding: 16rpx;
  background: #f0f9ff;
  border-radius: 8rpx;
  border-left: 4rpx solid #007aff;
}

.note-text {
  font-size: 24rpx;
  color: #007aff;
  line-height: 1.4;
}

.mock-search-bar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx 30rpx;
  padding-bottom: 30rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
}

.mock-input {
  flex: 1;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 30rpx;
  padding: 24rpx 30rpx;
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.mock-placeholder {
  font-size: 28rpx;
  color: #666;
}

.mock-cancel {
  font-size: 28rpx;
  color: #fff;
  font-weight: 500;
}

.test-btn {
  width: 100%;
  height: 80rpx;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #f5f7fa;
  color: #606266;
  margin-bottom: 16rpx;
}

.test-btn.primary {
  background: #007aff;
  color: #fff;
}

.test-btn:active {
  opacity: 0.8;
}

.fix-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.fix-item {
  font-size: 26rpx;
  color: #303133;
  line-height: 1.6;
  padding: 12rpx 16rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
}
</style>
