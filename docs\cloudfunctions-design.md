# 云函数架构设计

## 云函数列表

### 1. 用户认证相关 (user-auth)
**功能：** 用户注册、登录、信息管理
**接口：**
- `register` - 用户注册
- `login` - 用户登录
- `logout` - 用户登出
- `getUserInfo` - 获取用户信息
- `updateProfile` - 更新用户资料
- `uploadAvatar` - 上传头像
- `verifyStudent` - 学生身份认证
- `verifyLandlord` - 房东身份认证

### 2. 房源管理 (house-management)
**功能：** 房源的增删改查操作
**接口：**
- `publishHouse` - 发布房源
- `updateHouse` - 更新房源信息
- `deleteHouse` - 删除房源
- `getHouseDetail` - 获取房源详情
- `getHouseList` - 获取房源列表
- `searchHouses` - 搜索房源
- `getMyHouses` - 获取我的发布
- `uploadHouseImages` - 上传房源图片
- `updateHouseStatus` - 更新房源状态

### 3. 收藏管理 (favorite-management)
**功能：** 房源收藏相关操作
**接口：**
- `addFavorite` - 添加收藏
- `removeFavorite` - 取消收藏
- `getFavoriteList` - 获取收藏列表
- `checkFavoriteStatus` - 检查收藏状态

### 4. 预约管理 (appointment-management)
**功能：** 看房预约相关操作
**接口：**
- `createAppointment` - 创建预约
- `updateAppointment` - 更新预约状态
- `cancelAppointment` - 取消预约
- `getAppointmentList` - 获取预约列表
- `getLandlordAppointments` - 获取房东的预约

### 5. 消息管理 (message-management)
**功能：** 消息通知相关操作
**接口：**
- `sendMessage` - 发送消息
- `getMessageList` - 获取消息列表
- `markAsRead` - 标记已读
- `getUnreadCount` - 获取未读数量

### 6. 文件上传 (file-upload)
**功能：** 文件上传处理
**接口：**
- `uploadImage` - 上传图片
- `deleteImage` - 删除图片
- `getUploadToken` - 获取上传凭证

### 7. 系统管理 (system-management)
**功能：** 系统配置和管理
**接口：**
- `getSystemConfig` - 获取系统配置
- `updateSystemConfig` - 更新系统配置
- `getStatistics` - 获取统计数据
- `auditHouse` - 房源审核
- `manageUser` - 用户管理
- `handleReport` - 处理举报

### 8. 地理位置服务 (location-service)
**功能：** 地理位置相关服务
**接口：**
- `geocoding` - 地址转坐标
- `reverseGeocoding` - 坐标转地址
- `getNearbyHouses` - 获取附近房源
- `getDistrictList` - 获取区域列表

## 云函数通用结构

```javascript
// 云函数入口文件
'use strict';

const uniID = require('uni-id-common');

exports.main = async (event, context) => {
  const { action, data } = event;
  
  // 统一的错误处理
  try {
    // 根据action调用对应的处理函数
    switch (action) {
      case 'actionName':
        return await handleAction(event, context);
      default:
        return {
          code: 400,
          message: '无效的操作'
        };
    }
  } catch (error) {
    console.error('云函数执行错误:', error);
    return {
      code: 500,
      message: '服务器内部错误',
      error: error.message
    };
  }
};

// 具体的处理函数
async function handleAction(event, context) {
  // 权限验证
  const payload = await uniID.checkToken(event.uniIdToken);
  if (payload.code !== 0) {
    return payload;
  }
  
  // 业务逻辑处理
  // ...
  
  return {
    code: 0,
    message: '操作成功',
    data: result
  };
}
```

## 数据库权限配置

### DB Schema权限设计
- **houses表：** 
  - read: 所有用户可读
  - create: 认证用户可创建
  - update: 仅房东本人可更新
  - delete: 仅房东本人可删除

- **favorites表：**
  - read: 仅用户本人可读
  - create: 认证用户可创建
  - delete: 仅用户本人可删除

- **appointments表：**
  - read: 相关用户可读（预约者和房东）
  - create: 认证用户可创建
  - update: 相关用户可更新

- **messages表：**
  - read: 仅接收者可读
  - create: 认证用户可创建

## 错误码定义

```javascript
const ERROR_CODES = {
  SUCCESS: 0,
  INVALID_PARAM: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  SERVER_ERROR: 500,
  
  // 业务错误码
  USER_NOT_EXIST: 1001,
  HOUSE_NOT_EXIST: 1002,
  ALREADY_FAVORITED: 1003,
  APPOINTMENT_CONFLICT: 1004,
  INSUFFICIENT_PERMISSION: 1005
};
```
