<template>
  <view class="messages-container">
    <!-- 消息类型筛选 -->
    <view class="type-tabs">
      <view 
        class="tab-item" 
        v-for="(tab, index) in typeTabs" 
        :key="index"
        :class="{ active: currentTab === index }"
        @click="switchTab(index)"
      >
        <text class="tab-text">{{ tab.label }}</text>
        <text class="tab-count" v-if="tab.count > 0">{{ tab.count }}</text>
      </view>
    </view>
    
    <!-- 操作栏 -->
    <view class="action-bar" v-if="messageList.length > 0">
      <view class="action-left">
        <text class="unread-count">{{ unreadCount }}条未读</text>
      </view>
      <view class="action-right">
        <text class="mark-all-read" @click="markAllAsRead" v-if="unreadCount > 0">全部已读</text>
      </view>
    </view>
    
    <!-- 消息列表 -->
    <scroll-view 
      class="message-list" 
      scroll-y 
      @scrolltolower="loadMore"
      refresher-enabled
      @refresherrefresh="onRefresh"
      :refresher-triggered="refreshing"
    >
      <view 
        class="message-item" 
        v-for="message in messageList" 
        :key="message._id"
        :class="{ unread: !message.is_read }"
        @click="readMessage(message)"
      >
        <view class="message-header">
          <view class="message-type" :class="message.type">
            {{ getTypeText(message.type) }}
          </view>
          <text class="message-time">{{ formatRelativeTime(message.create_date) }}</text>
        </view>
        
        <view class="message-content">
          <text class="message-title">{{ message.title }}</text>
          <text class="message-text">{{ message.content }}</text>
        </view>
        
        <view class="message-footer" v-if="message.from_user">
          <view class="sender-info">
            <image 
              class="sender-avatar" 
              :src="message.from_user.avatar || '/static/default-avatar.png'" 
              mode="aspectFill"
            ></image>
            <text class="sender-name">{{ message.from_user.nickname || '系统消息' }}</text>
          </view>
          <view class="read-status">
            <uni-icons 
              v-if="!message.is_read" 
              type="circle" 
              size="8" 
              color="#ff4757"
            ></uni-icons>
            <text v-else class="read-text">已读</text>
          </view>
        </view>
      </view>
      
      <!-- 加载状态 -->
      <view class="load-status" v-if="messageList.length > 0">
        <text v-if="loading">加载中...</text>
        <text v-else-if="noMore">没有更多了</text>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" v-if="!loading && messageList.length === 0">
        <image src="/static/empty-message.png" mode="aspectFit"></image>
        <text class="empty-text">暂无消息</text>
        <text class="empty-tip">消息通知会在这里显示</text>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import request from '@/utils/request.js'
import { formatRelativeTime } from '@/utils/common.js'
import { MESSAGE_TYPES } from '@/common/config.js'

export default {
  data() {
    return {
      currentTab: 0,
      typeTabs: [
        { label: '全部', value: '', count: 0 },
        { label: '系统', value: 'system', count: 0 },
        { label: '预约', value: 'appointment', count: 0 },
        { label: '联系', value: 'contact', count: 0 }
      ],
      messageList: [],
      unreadCount: 0,
      loading: false,
      refreshing: false,
      noMore: false,
      page: 1,
      pageSize: 10
    }
  },
  methods: {
    formatRelativeTime,
    
    // 获取消息类型文本
    getTypeText(type) {
      return MESSAGE_TYPES[type] || type
    },
    
    // 切换标签
    switchTab(index) {
      this.currentTab = index
      this.loadMessageList(true)
    },
    
    // 加载消息列表
    async loadMessageList(refresh = false) {
      if (this.loading) return
      
      this.loading = true
      
      if (refresh) {
        this.page = 1
        this.noMore = false
      }
      
      try {
        const params = {
          action: 'getMessageList',
          data: {
            page: this.page,
            pageSize: this.pageSize
          }
        }
        
        // 添加类型筛选
        const currentType = this.typeTabs[this.currentTab].value
        if (currentType) {
          params.data.type = currentType
        }
        
        const result = await request.callFunction('message-management', params)
        
        if (result.code === 0) {
          const { list, total } = result.data
          
          if (refresh) {
            this.messageList = list
          } else {
            this.messageList.push(...list)
          }
          
          this.page++
          this.noMore = this.messageList.length >= total
        }
      } catch (error) {
        console.error('加载消息列表失败:', error)
      } finally {
        this.loading = false
        this.refreshing = false
      }
    },
    
    // 加载未读消息数量
    async loadUnreadCount() {
      try {
        const result = await request.callFunction('message-management', {
          action: 'getUnreadCount'
        })
        
        if (result.code === 0) {
          const counts = result.data
          this.unreadCount = counts.total
          
          // 更新标签计数
          this.typeTabs[0].count = counts.total
          this.typeTabs[1].count = counts.system
          this.typeTabs[2].count = counts.appointment
          this.typeTabs[3].count = counts.contact
        }
      } catch (error) {
        console.error('加载未读数量失败:', error)
      }
    },
    
    // 加载更多
    loadMore() {
      if (!this.noMore && !this.loading) {
        this.loadMessageList()
      }
    },
    
    // 下拉刷新
    onRefresh() {
      this.refreshing = true
      this.loadMessageList(true)
      this.loadUnreadCount()
    },
    
    // 阅读消息
    async readMessage(message) {
      // 如果未读，标记为已读
      if (!message.is_read) {
        try {
          const result = await request.callFunction('message-management', {
            action: 'markAsRead',
            data: {
              message_ids: [message._id]
            }
          })
          
          if (result.code === 0) {
            message.is_read = true
            this.unreadCount = Math.max(0, this.unreadCount - 1)
            
            // 更新标签计数
            this.updateTabCounts(-1, message.type)
          }
        } catch (error) {
          console.error('标记已读失败:', error)
        }
      }
      
      // 如果有关联内容，跳转到相应页面
      if (message.related_id) {
        this.handleMessageAction(message)
      }
    },
    
    // 处理消息点击动作
    handleMessageAction(message) {
      switch (message.type) {
        case 'appointment':
          // 跳转到预约详情或预约列表
          uni.navigateTo({
            url: '/pages/user/appointments'
          })
          break
        case 'system':
          // 根据内容判断跳转
          if (message.content.includes('房源')) {
            uni.navigateTo({
              url: `/pages/house/detail?id=${message.related_id}`
            })
          }
          break
        case 'contact':
          // 跳转到联系相关页面
          break
        default:
          break
      }
    },
    
    // 全部标记为已读
    async markAllAsRead() {
      if (this.unreadCount === 0) return
      
      uni.showModal({
        title: '提示',
        content: '确定要将所有消息标记为已读吗？',
        success: async (res) => {
          if (res.confirm) {
            try {
              // 获取所有未读消息ID
              const unreadIds = this.messageList
                .filter(msg => !msg.is_read)
                .map(msg => msg._id)
              
              if (unreadIds.length === 0) return
              
              const result = await request.callFunction('message-management', {
                action: 'markAsRead',
                data: {
                  message_ids: unreadIds
                }
              })
              
              if (result.code === 0) {
                // 更新本地数据
                this.messageList.forEach(msg => {
                  if (!msg.is_read) {
                    msg.is_read = true
                  }
                })
                
                this.unreadCount = 0
                
                // 重置标签计数
                this.typeTabs.forEach(tab => {
                  tab.count = 0
                })
                
                uni.showToast({
                  title: '已全部标记为已读',
                  icon: 'success'
                })
              }
            } catch (error) {
              console.error('批量标记已读失败:', error)
            }
          }
        }
      })
    },
    
    // 更新标签计数
    updateTabCounts(delta, messageType) {
      // 更新总数
      this.typeTabs[0].count = Math.max(0, this.typeTabs[0].count + delta)
      
      // 更新对应类型计数
      const typeIndex = this.typeTabs.findIndex(tab => tab.value === messageType)
      if (typeIndex > 0) {
        this.typeTabs[typeIndex].count = Math.max(0, this.typeTabs[typeIndex].count + delta)
      }
    }
  },
  
  onLoad() {
    this.loadMessageList(true)
    this.loadUnreadCount()
  },
  
  onShow() {
    // 从其他页面返回时刷新
    this.loadUnreadCount()
  },
  
  onPullDownRefresh() {
    this.onRefresh()
    uni.stopPullDownRefresh()
  }
}
</script>

<style scoped>
.messages-container {
  height: 100vh;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
}

.type-tabs {
  background: #fff;
  display: flex;
  border-bottom: 1rpx solid #f0f0f0;
}

.tab-item {
  flex: 1;
  padding: 30rpx 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  position: relative;
}

.tab-item.active {
  color: #007aff;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background: #007aff;
  border-radius: 2rpx;
}

.tab-text {
  font-size: 28rpx;
  color: #333;
}

.tab-item.active .tab-text {
  color: #007aff;
  font-weight: 500;
}

.tab-count {
  background: #ff4757;
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 10rpx;
  min-width: 32rpx;
  text-align: center;
}

.action-bar {
  background: #fff;
  padding: 20rpx 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1rpx solid #f0f0f0;
}

.unread-count {
  font-size: 26rpx;
  color: #666;
}

.mark-all-read {
  font-size: 26rpx;
  color: #007aff;
}

.message-list {
  flex: 1;
  padding: 20rpx;
}

.message-item {
  background: #fff;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  position: relative;
}

.message-item.unread {
  border-left: 6rpx solid #007aff;
}

.message-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15rpx;
}

.message-type {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  color: #fff;
}

.message-type.system {
  background: #007aff;
}

.message-type.appointment {
  background: #10c560;
}

.message-type.contact {
  background: #ff9800;
}

.message-time {
  font-size: 24rpx;
  color: #999;
}

.message-content {
  margin-bottom: 20rpx;
}

.message-title {
  display: block;
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
}

.message-text {
  display: block;
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

.message-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sender-info {
  display: flex;
  align-items: center;
}

.sender-avatar {
  width: 40rpx;
  height: 40rpx;
  border-radius: 20rpx;
  margin-right: 12rpx;
}

.sender-name {
  font-size: 24rpx;
  color: #999;
}

.read-status {
  display: flex;
  align-items: center;
}

.read-text {
  font-size: 22rpx;
  color: #ccc;
}

.load-status {
  text-align: center;
  padding: 40rpx;
  color: #999;
  font-size: 28rpx;
}

.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
}

.empty-state image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
}

.empty-text {
  display: block;
  font-size: 28rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.empty-tip {
  display: block;
  font-size: 24rpx;
  color: #ccc;
}
</style>
