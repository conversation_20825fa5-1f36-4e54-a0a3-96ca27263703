# 手动下载图片资源指南

由于自动下载脚本可能受到网络限制，这里提供手动下载的详细指南。

## 📋 需要下载的图片列表

### 1. 应用Logo和图标

#### logo.png (120x120px)
- **推荐网站**: Iconfont (https://www.iconfont.cn/)
- **搜索关键词**: 房屋、home、house
- **建议样式**: 简洁的房屋图标，蓝色或绿色
- **下载步骤**:
  1. 访问 https://www.iconfont.cn/
  2. 搜索 "房屋" 或 "home"
  3. 选择合适的图标
  4. 下载PNG格式，120x120px
  5. 重命名为 `logo.png`

#### default-avatar.png (200x200px)
- **推荐网站**: Unsplash (https://unsplash.com/)
- **搜索关键词**: profile, avatar, person
- **建议样式**: 友好的人物头像或通用头像图标
- **直接链接**: https://unsplash.com/photos/a-man-in-a-blue-shirt-is-smiling-at-the-camera-472oHiuPaTc
- **下载步骤**:
  1. 访问上述链接或搜索类似图片
  2. 点击下载按钮
  3. 选择 Small (400x400) 尺寸
  4. 使用图片编辑工具裁剪为 200x200px
  5. 重命名为 `default-avatar.png`

### 2. 房源相关图片

#### default-house.png (400x300px)
- **推荐网站**: Unsplash (https://unsplash.com/)
- **搜索关键词**: apartment, modern house, interior
- **建议样式**: 现代简洁的室内或建筑外观
- **推荐图片**:
  - https://unsplash.com/photos/white-and-brown-wooden-house-near-green-trees-during-daytime-1ddol8rgUH8
  - https://unsplash.com/photos/white-wooden-house-surrounded-by-green-trees-during-daytime-mp0bgaS_d1c
- **下载步骤**:
  1. 选择合适的房屋图片
  2. 下载 Regular (1080x720) 尺寸
  3. 裁剪为 400x300px
  4. 重命名为 `default-house.png`

#### test-house.jpg (400x300px)
- **推荐网站**: Pexels (https://www.pexels.com/)
- **搜索关键词**: apartment interior, living room
- **推荐图片**:
  - https://www.pexels.com/photo/spacious-living-room-interior-1571460/
  - https://www.pexels.com/photo/interior-of-modern-apartment-1643383/
- **下载步骤**:
  1. 选择室内装修图片
  2. 下载 Medium 尺寸
  3. 调整为 400x300px
  4. 保存为 `test-house.jpg`

### 3. 轮播图

#### banner1.jpg (750x300px) - 房屋外观
- **推荐图片**: https://unsplash.com/photos/white-concrete-building-during-daytime-1ddol8rgUH8
- **下载步骤**:
  1. 下载 Large 尺寸
  2. 裁剪为 750x300px 比例
  3. 重命名为 `banner1.jpg`

#### banner2.jpg (750x300px) - 室内装修
- **推荐图片**: https://unsplash.com/photos/white-wooden-table-with-chairs-1571460/
- **下载步骤**:
  1. 下载 Large 尺寸
  2. 裁剪为 750x300px 比例
  3. 重命名为 `banner2.jpg`

#### banner3.jpg (750x300px) - 城市建筑
- **推荐图片**: https://unsplash.com/photos/city-buildings-during-daytime-1643383/
- **下载步骤**:
  1. 下载 Large 尺寸
  2. 裁剪为 750x300px 比例
  3. 重命名为 `banner3.jpg`

### 4. 空状态图片

#### empty-default.png (200x200px)
- **推荐网站**: Undraw (https://undraw.co/)
- **搜索关键词**: empty, no data
- **建议样式**: 简洁的插画风格
- **下载步骤**:
  1. 访问 https://undraw.co/
  2. 搜索 "empty" 或 "void"
  3. 选择合适的插画
  4. 下载 PNG 格式
  5. 调整为 200x200px
  6. 重命名为 `empty-default.png`

#### empty-house.png (200x200px)
- **推荐网站**: Undraw (https://undraw.co/)
- **搜索关键词**: house, home, real estate
- **下载步骤**: 同上，选择房屋相关插画

#### empty-favorite.png (200x200px)
- **推荐网站**: Undraw (https://undraw.co/)
- **搜索关键词**: heart, favorite, love
- **下载步骤**: 同上，选择收藏相关插画

#### empty-search.png (200x200px)
- **推荐网站**: Undraw (https://undraw.co/)
- **搜索关键词**: search, not found
- **下载步骤**: 同上，选择搜索相关插画

### 5. 地图标记

#### map-marker.png (30x30px)
- **推荐网站**: Iconfont (https://www.iconfont.cn/)
- **搜索关键词**: 地图标记、location、marker
- **下载步骤**:
  1. 搜索地图标记图标
  2. 选择红色或蓝色的标记
  3. 下载 PNG 格式，30x30px
  4. 重命名为 `map-marker.png`

## 🛠️ 图片处理工具

### 在线工具
1. **Canva** (https://www.canva.com/)
   - 免费的在线设计工具
   - 可以调整图片尺寸和格式

2. **TinyPNG** (https://tinypng.com/)
   - 图片压缩工具
   - 减小文件大小

3. **稿定设计** (https://www.gaoding.com/)
   - 中文界面的设计工具
   - 支持批量处理

### 桌面工具
1. **GIMP** (免费)
   - 功能强大的图片编辑软件
   - 支持批量处理

2. **Photoshop** (付费)
   - 专业图片编辑软件
   - 最佳的处理效果

## 📁 文件放置

下载完成后，将所有图片放置到项目的 `static` 目录下：

```
static/
├── logo.png
├── default-avatar.png
├── default-house.png
├── empty-default.png
├── empty-house.png
├── empty-favorite.png
├── empty-search.png
├── banner1.jpg
├── banner2.jpg
├── banner3.jpg
├── map-marker.png
└── test-house.jpg
```

## ✅ 验证清单

下载完成后，请检查以下项目：

- [ ] 所有图片文件都已下载
- [ ] 文件名称正确无误
- [ ] 图片尺寸符合要求
- [ ] 文件大小合理（单张不超过500KB）
- [ ] 图片格式正确（PNG/JPG）
- [ ] 图片质量清晰
- [ ] 放置在正确的目录

## 🚀 快速验证

在小程序中运行以下代码验证图片是否正确加载：

```javascript
// 在页面的 onLoad 中添加
const images = [
  '/static/logo.png',
  '/static/default-avatar.png',
  '/static/default-house.png',
  '/static/empty-default.png',
  '/static/banner1.jpg'
];

images.forEach(image => {
  uni.getImageInfo({
    src: image,
    success: (res) => {
      console.log(`✅ ${image} 加载成功`, res);
    },
    fail: (error) => {
      console.error(`❌ ${image} 加载失败`, error);
    }
  });
});
```

## 💡 小贴士

1. **版权注意**: 确保使用的图片支持商业使用
2. **统一风格**: 选择风格一致的图片，保持视觉统一
3. **文件大小**: 控制图片文件大小，提升加载速度
4. **备用方案**: 准备多个版本的图片作为备选
5. **定期更新**: 根据用户反馈定期更新图片资源

完成以上步骤后，您的应用就拥有了完整的图片资源配置！
