<template>
  <view class="publish-container">
    <!-- 内容区域 -->
    <scroll-view class="content" scroll-y>
      <form @submit="handleSubmit">
      <!-- 基本信息 -->
      <view class="form-section">
        <view class="section-title">基本信息</view>
        
        <view class="form-item">
          <text class="label">房源标题 *</text>
          <input 
            class="input" 
            type="text" 
            placeholder="请输入房源标题" 
            v-model="form.title"
            maxlength="50"
          />
        </view>
        
        <view class="form-item">
          <text class="label">房源描述</text>
          <textarea 
            class="textarea" 
            placeholder="请详细描述房源情况，如周边环境、交通等" 
            v-model="form.description"
            maxlength="500"
          ></textarea>
        </view>
        
        <view class="form-item">
          <text class="label">房源类型 *</text>
          <picker 
            mode="selector" 
            :range="houseTypes" 
            range-key="label"
            :value="typeIndex"
            @change="onTypeChange"
          >
            <view class="picker">
              {{ form.type ? getTypeLabel(form.type) : '请选择房源类型' }}
              <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
            </view>
          </picker>
        </view>
      </view>
      
      <!-- 房屋信息 -->
      <view class="form-section">
        <view class="section-title">房屋信息</view>
        
        <view class="form-row">
          <view class="form-item half">
            <text class="label">租金(元/月) *</text>
            <input 
              class="input" 
              type="number" 
              placeholder="租金" 
              v-model="form.price"
            />
          </view>
          <view class="form-item half">
            <text class="label">押金(元)</text>
            <input 
              class="input" 
              type="number" 
              placeholder="押金" 
              v-model="form.deposit"
            />
          </view>
        </view>
        
        <view class="form-row">
          <view class="form-item half">
            <text class="label">面积(㎡)</text>
            <input 
              class="input" 
              type="number" 
              placeholder="面积" 
              v-model="form.area"
            />
          </view>
          <view class="form-item half">
            <text class="label">朝向</text>
            <picker 
              mode="selector" 
              :range="orientations"
              :value="orientationIndex"
              @change="onOrientationChange"
            >
              <view class="picker">
                {{ form.orientation || '请选择' }}
                <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
              </view>
            </picker>
          </view>
        </view>
        
        <view class="form-row">
          <view class="form-item third">
            <text class="label">房间数</text>
            <input 
              class="input" 
              type="number" 
              placeholder="房间" 
              v-model="form.room_count"
            />
          </view>
          <view class="form-item third">
            <text class="label">客厅数</text>
            <input 
              class="input" 
              type="number" 
              placeholder="客厅" 
              v-model="form.hall_count"
            />
          </view>
          <view class="form-item third">
            <text class="label">卫生间</text>
            <input 
              class="input" 
              type="number" 
              placeholder="卫生间" 
              v-model="form.bathroom_count"
            />
          </view>
        </view>
        
        <view class="form-row">
          <view class="form-item half">
            <text class="label">楼层</text>
            <input 
              class="input" 
              type="number" 
              placeholder="楼层" 
              v-model="form.floor"
            />
          </view>
          <view class="form-item half">
            <text class="label">总楼层</text>
            <input 
              class="input" 
              type="number" 
              placeholder="总楼层" 
              v-model="form.total_floors"
            />
          </view>
        </view>
        
        <view class="form-item">
          <text class="label">装修情况</text>
          <picker 
            mode="selector" 
            :range="decorationTypes"
            :value="decorationIndex"
            @change="onDecorationChange"
          >
            <view class="picker">
              {{ form.decoration || '请选择装修情况' }}
              <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
            </view>
          </picker>
        </view>
      </view>
      
      <!-- 房屋设施 -->
      <view class="form-section">
        <view class="section-title">房屋设施</view>
        <view class="facilities-grid">
          <view
            class="facility-item"
            v-for="facility in facilitiesOptions"
            :key="facility.value"
            :class="{ active: form.facilities.includes(facility.value) }"
            @click="toggleFacility(facility.value)"
          >
            <view class="facility-icon">{{ facility.icon || '📦' }}</view>
            <text class="facility-text">{{ facility.label }}</text>
            <view class="facility-check" v-if="form.facilities.includes(facility.value)">
              <uni-icons type="checkmarkempty" size="16" color="#fff"></uni-icons>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 房源图片 -->
      <view class="form-section">
        <view class="section-title">房源图片</view>
        <view class="image-upload">
          <view class="image-list">
            <view 
              class="image-item" 
              v-for="(image, index) in form.images" 
              :key="index"
            >
              <image :src="image" mode="aspectFill"></image>
              <view class="image-delete" @click="removeImage(index)">
                <uni-icons type="close" size="16" color="#fff"></uni-icons>
              </view>
            </view>
            <view 
              class="image-add" 
              v-if="form.images.length < 9"
              @click="chooseImage"
            >
              <uni-icons type="plus" size="32" color="#ccc"></uni-icons>
              <text class="add-text">添加图片</text>
            </view>
          </view>
          <text class="image-tip">最多上传9张图片，第一张为封面图</text>
        </view>
      </view>
      
      <!-- 位置信息 -->
      <view class="form-section">
        <view class="section-title">位置信息</view>
        
        <view class="form-item">
          <text class="label">详细地址 *</text>
          <input 
            class="input" 
            type="text" 
            placeholder="请输入详细地址" 
            v-model="form.location.address"
          />
        </view>
        
        <view class="form-item">
          <text class="label">选择位置</text>
          <view class="location-picker" @click="chooseLocation">
            <view class="location-content">
              <text class="location-text" :class="{ 'placeholder': !form.location.address }">
                {{ form.location.address || '点击选择位置' }}
              </text>
              <text v-if="form.location.name" class="location-name">{{ form.location.name }}</text>
            </view>
            <uni-icons type="location" size="16" color="#007aff"></uni-icons>
          </view>
        </view>
      </view>
      
      <!-- 联系方式 -->
      <view class="form-section">
        <view class="section-title">联系方式</view>
        
        <view class="form-item">
          <text class="label">联系人 *</text>
          <input 
            class="input" 
            type="text" 
            placeholder="请输入联系人姓名" 
            v-model="form.contact.name"
          />
        </view>
        
        <view class="form-item">
          <text class="label">联系电话 *</text>
          <input 
            class="input" 
            type="number" 
            placeholder="请输入联系电话" 
            v-model="form.contact.phone"
          />
        </view>
        
        <view class="form-item">
          <text class="label">微信号</text>
          <input 
            class="input" 
            type="text" 
            placeholder="请输入微信号" 
            v-model="form.contact.wechat"
          />
        </view>
      </view>
      
      <!-- 提交按钮 -->
      <view class="submit-section">
        <button class="submit-btn" @click="handleSubmit" :disabled="!canSubmit">
          {{ loading ? '发布中...' : '发布房源' }}
        </button>
        <view class="debug-info" style="margin-top: 20rpx; font-size: 24rpx; color: #666;">
          调试信息: canSubmit={{ canSubmit }}, loading={{ loading }}
        </view>
      </view>
    </form>
    </scroll-view>
  </view>
</template>

<script>
import request from '@/utils/request.js'
import { validatePhone } from '@/utils/common.js'
import { HOUSE_TYPES, ORIENTATIONS, DECORATION_TYPES, FACILITIES } from '@/common/config.js'

export default {
  data() {
    return {
      form: {
        title: '',
        description: '',
        type: '',
        price: '',
        deposit: '',
        area: '',
        room_count: '',
        hall_count: '',
        bathroom_count: '',
        floor: '',
        total_floors: '',
        orientation: '',
        decoration: '',
        facilities: [],
        images: [],
        location: {
          province: '',
          city: '',
          district: '',
          address: '',
          longitude: '',
          latitude: ''
        },
        contact: {
          name: '',
          phone: '',
          wechat: ''
        }
      },
      loading: false,
      
      // 选择器数据
      houseTypes: HOUSE_TYPES,
      orientations: ORIENTATIONS,
      decorationTypes: DECORATION_TYPES,
      facilitiesOptions: FACILITIES,
      
      // 选择器索引
      typeIndex: 0,
      orientationIndex: 0,
      decorationIndex: 0
    }
  },
  computed: {
    canSubmit() {
      const { title, type, price, location, contact } = this.form
      return title.trim() && 
             type && 
             price && 
             location.address.trim() && 
             contact.name.trim() && 
             contact.phone.trim() && 
             !this.loading
    }
  },
  methods: {
    // 获取类型标签
    getTypeLabel(value) {
      const type = this.houseTypes.find(item => item.value === value)
      return type ? type.label : value
    },
    
    // 房源类型选择
    onTypeChange(e) {
      const index = e.detail.value
      this.typeIndex = index
      this.form.type = this.houseTypes[index].value

      console.log('类型选择变化:', {
        index: index,
        selectedType: this.houseTypes[index],
        formType: this.form.type
      })
    },
    
    // 朝向选择
    onOrientationChange(e) {
      const index = e.detail.value
      this.orientationIndex = index
      this.form.orientation = this.orientations[index]
    },
    
    // 装修情况选择
    onDecorationChange(e) {
      const index = e.detail.value
      this.decorationIndex = index
      this.form.decoration = this.decorationTypes[index]
    },
    
    // 切换设施
    toggleFacility(value) {
      const index = this.form.facilities.indexOf(value)
      if (index > -1) {
        this.form.facilities.splice(index, 1)
      } else {
        this.form.facilities.push(value)
      }
    },
    
    // 选择图片
    chooseImage() {
      const remainCount = 9 - this.form.images.length
      uni.chooseImage({
        count: remainCount,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          this.form.images.push(...res.tempFilePaths)
        }
      })
    },
    
    // 删除图片
    removeImage(index) {
      this.form.images.splice(index, 1)
    },
    
    // 选择位置
    chooseLocation() {
      // 直接调用选择位置API
      uni.chooseLocation({
            success: (res) => {
              console.log('选择位置成功:', res)

              this.form.location = {
                ...this.form.location,
                address: res.address,
                longitude: res.longitude,
                latitude: res.latitude,
                name: res.name || ''
              }

              // 解析城市和区县信息
              this.parseLocationInfo(res.address)

              uni.showToast({
                title: '位置选择成功',
                icon: 'success'
              })
            },
            fail: (error) => {
              console.error('选择位置失败:', error)

              let errorMsg = '选择位置失败'
              if (error.errMsg.includes('cancel')) {
                errorMsg = '已取消选择位置'
              } else if (error.errMsg.includes('auth')) {
                errorMsg = '需要位置权限'
              }

              uni.showToast({
                title: errorMsg,
                icon: 'none'
              })
            }
          })
    },

    // 解析位置信息
    parseLocationInfo(address) {
      try {
        // 更精确的地址解析
        const provinceMatch = address.match(/(.+?省)/)
        const cityMatch = address.match(/(.+?市)/)
        const districtMatch = address.match(/(.+?[区县])/)

        if (provinceMatch) {
          this.form.location.province = provinceMatch[1]
        }
        if (cityMatch) {
          this.form.location.city = cityMatch[1]
        }
        if (districtMatch) {
          this.form.location.district = districtMatch[1]
        }

        console.log('解析后的位置信息:', this.form.location)
      } catch (error) {
        console.error('解析位置信息失败:', error)
      }
    },
    
    // 提交表单
    async handleSubmit() {
      console.log('=== 开始提交表单 ===')
      console.log('canSubmit:', this.canSubmit)
      console.log('loading:', this.loading)
      console.log('表单数据:', this.form)

      // 检查用户登录状态
      const token = uni.getStorageSync('uni_id_token')
      console.log('用户token:', token ? '已登录' : '未登录')

      if (!this.canSubmit) {
        console.log('表单验证不通过')
        uni.showToast({
          title: '请填写完整信息',
          icon: 'none'
        })
        return
      }

      // 表单验证
      if (!validatePhone(this.form.contact.phone)) {
        uni.showToast({
          title: '联系电话格式不正确',
          icon: 'none'
        })
        return
      }

      if (this.form.price <= 0) {
        uni.showToast({
          title: '租金必须大于0',
          icon: 'none'
        })
        return
      }

      // 检查位置信息
      if (!this.form.location.address) {
        uni.showToast({
          title: '请选择房源位置',
          icon: 'none'
        })
        return
      }

      this.loading = true

      try {
        console.log('准备调用云函数...')

        const submitData = {
          ...this.form,
          price: Number(this.form.price),
          deposit: Number(this.form.deposit) || 0,
          area: Number(this.form.area) || 0,
          room_count: Number(this.form.room_count) || 0,
          hall_count: Number(this.form.hall_count) || 0,
          bathroom_count: Number(this.form.bathroom_count) || 0,
          floor: Number(this.form.floor) || 0,
          total_floors: Number(this.form.total_floors) || 0
        }

        console.log('提交数据:', submitData)
        console.log('房源类型详细信息:', {
          typeIndex: this.typeIndex,
          formType: this.form.type,
          houseTypes: this.houseTypes,
          selectedTypeInfo: this.houseTypes[this.typeIndex]
        })

        const result = await request.callFunction('house-management', {
          action: 'publishHouse',
          data: submitData
        })

        console.log('云函数返回结果:', result)

        if (result.code === 0) {
          uni.showToast({
            title: '发布成功',
            icon: 'success'
          })

          setTimeout(() => {
            uni.navigateBack()
          }, 1500)
        } else {
          uni.showToast({
            title: result.message || '发布失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('发布失败:', error)
        uni.showToast({
          title: '发布失败，请重试',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    }
  },

  onLoad() {
    // 设置默认房源类型为整租
    if (!this.form.type && this.houseTypes.length > 0) {
      this.form.type = this.houseTypes[0].value // 'whole'
      this.typeIndex = 0
      console.log('设置默认房源类型:', this.form.type)
    }
  }
}
</script>

<style scoped>
.publish-container {
  background: #f8f9fa;
  min-height: 100vh;
}



/* 内容区域 */
.content {
  background: #f8f9fa;
  min-height: 100vh;
  padding: 20rpx 0 120rpx;
}

.form-section {
  background: #fff;
  margin: 0 30rpx 40rpx;
  border-radius: 24rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 40rpx;
  padding-bottom: 30rpx;
  border-bottom: 2rpx solid #f5f7fa;
  position: relative;
}

.section-title::before {
  content: '';
  position: absolute;
  bottom: -2rpx;
  left: 0;
  width: 60rpx;
  height: 4rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 2rpx;
}

.form-item {
  margin-bottom: 40rpx; /* 增加表单项之间的间距 */
}

.form-row {
  display: flex;
  gap: 24rpx; /* 增加列之间的间距 */
  margin-bottom: 40rpx; /* 增加行之间的间距 */
}

.form-item.half {
  flex: 1;
}

.form-item.third {
  flex: 1;
}

.label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: 500;
  line-height: 1.4; /* 增加行高确保文字显示完整 */
  white-space: nowrap; /* 防止标签文字换行 */
}

.input, .textarea {
  width: 100%;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  padding: 28rpx 24rpx; /* 增加上下内边距 */
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  transition: all 0.3s ease;
  min-height: 96rpx; /* 确保输入框有足够高度 */
  line-height: 1.4;
}

.input:focus, .textarea:focus {
  border-color: #667eea;
  background: #fff;
  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
  outline: none;
}

.textarea {
  min-height: 180rpx; /* 增加文本域的最小高度 */
  resize: none;
  line-height: 1.6;
  padding: 28rpx 24rpx; /* 确保文本域也有足够的内边距 */
}

.picker {
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  padding: 28rpx 24rpx; /* 增加上下内边距 */
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 28rpx;
  color: #333;
  transition: all 0.3s ease;
  min-height: 96rpx; /* 增加最小高度 */
  box-sizing: border-box;
  line-height: 1.4;
}

.picker:active {
  border-color: #667eea;
  background: #fff;
  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
}

.facilities-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.facility-item {
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  padding: 24rpx;
  font-size: 26rpx;
  color: #666;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 120rpx;
}

.facility-item.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
  color: #fff;
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.3);
}

.facility-icon {
  font-size: 32rpx;
  margin-bottom: 12rpx;
}

.facility-text {
  font-size: 26rpx;
  font-weight: 500;
}

.facility-item.active .facility-text {
  color: #fff;
}

.facility-check {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 32rpx;
  height: 32rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-upload {
  margin-top: 30rpx;
}

.image-list {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.image-item {
  position: relative;
  aspect-ratio: 1;
  border-radius: 16rpx;
  overflow: hidden;
  background: #f8f9fa;
}

.image-item image {
  width: 100%;
  height: 100%;
}

.image-delete {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 48rpx;
  height: 48rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10rpx);
}

.image-add {
  aspect-ratio: 1;
  background: #f8f9fa;
  border: 2rpx dashed #e9ecef;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.image-add:active {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.05);
}

.add-text {
  font-size: 24rpx;
  color: #667eea;
  margin-top: 12rpx;
  font-weight: 500;
}

.image-tip {
  display: block;
  font-size: 24rpx;
  color: #667eea;
  margin-top: 20rpx;
  background: #f0f9ff;
  padding: 16rpx 20rpx;
  border-radius: 12rpx;
  border-left: 4rpx solid #667eea;
}

.location-picker {
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  padding: 28rpx 24rpx; /* 增加上下内边距 */
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s ease;
  min-height: 96rpx; /* 增加最小高度 */
  box-sizing: border-box;
}

.location-picker:active {
  border-color: #667eea;
  background: #fff;
  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
}

.location-content {
  flex: 1;
  display: flex;
  align-items: center;
}

.location-content::before {
  content: '📍';
  font-size: 32rpx;
  margin-right: 16rpx;
}

.location-text {
  font-size: 28rpx;
  color: #333;
  display: block;
  line-height: 1.4; /* 增加行高 */
  word-break: break-all; /* 允许长地址换行 */
}

.location-text.placeholder {
  color: #999;
}

.location-name {
  font-size: 24rpx;
  color: #666;
  margin-top: 8rpx;
  display: block;
  line-height: 1.4;
}

.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
}

.submit-btn {
  width: 100%;
  height: 96rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  border: none;
  border-radius: 24rpx;
  font-size: 32rpx;
  font-weight: bold;
  box-shadow: 0 8rpx 30rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.submit-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.submit-btn:active::before {
  left: 100%;
}

.submit-btn:active {
  transform: translateY(-2rpx);
  box-shadow: 0 12rpx 40rpx rgba(102, 126, 234, 0.4);
}

.submit-btn:disabled {
  background: #e9ecef;
  color: #999;
  box-shadow: none;
  transform: none;
}
</style>
