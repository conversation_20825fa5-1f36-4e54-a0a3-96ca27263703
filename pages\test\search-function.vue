<template>
  <view class="test-container">
    <view class="test-header">
      <text class="test-title">搜索功能测试</text>
      <text class="test-subtitle">验证搜索跳转和功能完整性</text>
    </view>
    
    <view class="test-content">
      <!-- 功能状态 -->
      <view class="status-section">
        <view class="status-title">🔧 修复状态</view>
        
        <view class="status-item success">
          <view class="status-icon">✅</view>
          <view class="status-text">
            <text class="status-name">TabBar 配置修复</text>
            <text class="status-desc">已将搜索页面从 tabBar 中移除</text>
          </view>
        </view>
        
        <view class="status-item success">
          <view class="status-icon">✅</view>
          <view class="status-text">
            <text class="status-name">跳转方法修复</text>
            <text class="status-desc">使用 navigateTo 替代 switchTab</text>
          </view>
        </view>
        
        <view class="status-item success">
          <view class="status-icon">✅</view>
          <view class="status-text">
            <text class="status-name">自定义导航栏</text>
            <text class="status-desc">搜索页面支持自定义导航栏</text>
          </view>
        </view>
      </view>
      
      <!-- 测试功能 -->
      <view class="test-section">
        <view class="section-title">🧪 功能测试</view>
        
        <view class="test-item">
          <view class="test-info">
            <text class="test-name">搜索页面跳转</text>
            <text class="test-desc">测试从当前页面跳转到搜索页面</text>
          </view>
          <button class="test-btn" @click="testSearch">测试</button>
        </view>
        
        <view class="test-item">
          <view class="test-info">
            <text class="test-name">首页搜索栏</text>
            <text class="test-desc">返回首页测试搜索栏点击</text>
          </view>
          <button class="test-btn" @click="testIndexSearch">测试</button>
        </view>
        
        <view class="test-item">
          <view class="test-info">
            <text class="test-name">搜索功能完整性</text>
            <text class="test-desc">检查搜索建议、历史记录等功能</text>
          </view>
          <button class="test-btn" @click="testSearchFeatures">测试</button>
        </view>
      </view>
      
      <!-- 预期结果 -->
      <view class="result-section">
        <view class="section-title">✨ 预期结果</view>
        
        <view class="result-list">
          <text class="result-item">• 点击搜索栏能正常跳转，无错误提示</text>
          <text class="result-item">• 搜索页面有美观的渐变背景</text>
          <text class="result-item">• 输入框有聚焦动画和毛玻璃效果</text>
          <text class="result-item">• 搜索建议实时显示</text>
          <text class="result-item">• 搜索历史记录功能正常</text>
          <text class="result-item">• 可以正常返回到上一页面</text>
        </view>
      </view>
      
      <!-- 操作按钮 -->
      <view class="action-section">
        <button class="action-btn primary" @click="testSearch">
          立即测试搜索
        </button>
        <button class="action-btn" @click="goToIndex">
          返回首页
        </button>
        <button class="action-btn" @click="goBack">
          返回上页
        </button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  methods: {
    testSearch() {
      uni.navigateTo({
        url: '/pages/search/search'
      });
    },
    
    testIndexSearch() {
      uni.showToast({
        title: '跳转到首页测试',
        icon: 'none'
      });
      
      setTimeout(() => {
        uni.switchTab({
          url: '/pages/index/index'
        });
      }, 1000);
    },
    
    testSearchFeatures() {
      uni.showModal({
        title: '功能测试说明',
        content: '即将跳转到搜索页面，请测试：\n1. 输入关键词查看搜索建议\n2. 查看搜索历史记录\n3. 测试清空功能\n4. 测试返回功能',
        success: (res) => {
          if (res.confirm) {
            this.testSearch();
          }
        }
      });
    },
    
    goToIndex() {
      uni.switchTab({
        url: '/pages/index/index'
      });
    },
    
    goBack() {
      uni.navigateBack();
    }
  }
}
</script>

<style scoped>
.test-container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  padding: 40rpx;
}

.test-header {
  text-align: center;
  margin-bottom: 60rpx;
  padding-top: 60rpx;
}

.test-title {
  display: block;
  font-size: 40rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 20rpx;
}

.test-subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.test-content {
  background: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.status-section, .test-section, .result-section {
  margin-bottom: 50rpx;
}

.status-title, .section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #303133;
  margin-bottom: 30rpx;
}

.status-item {
  display: flex;
  align-items: flex-start;
  padding: 20rpx;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
}

.status-item.success {
  background: #f0f9ff;
  border-left: 4rpx solid #10b981;
}

.status-icon {
  font-size: 24rpx;
  margin-right: 16rpx;
  margin-top: 4rpx;
}

.status-text {
  flex: 1;
}

.status-name {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8rpx;
}

.status-desc {
  display: block;
  font-size: 24rpx;
  color: #10b981;
  line-height: 1.4;
}

.test-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
}

.test-info {
  flex: 1;
}

.test-name {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8rpx;
}

.test-desc {
  display: block;
  font-size: 24rpx;
  color: #909399;
  line-height: 1.4;
}

.test-btn {
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
}

.result-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.result-item {
  font-size: 26rpx;
  color: #303133;
  line-height: 1.6;
  padding: 12rpx 16rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
}

.action-section {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.action-btn {
  height: 88rpx;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #f5f7fa;
  color: #606266;
}

.action-btn.primary {
  background: #007aff;
  color: #fff;
}

.action-btn:active {
  opacity: 0.8;
}
</style>
