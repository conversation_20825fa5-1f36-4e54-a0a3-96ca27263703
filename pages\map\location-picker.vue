<template>
  <view class="location-picker-container">
    <!-- 头部导航 -->
    <view class="header" :style="{ paddingTop: (statusBarHeight * 2 + 20) + 'rpx' }">
      <view class="header-content">
        <view class="back-btn" @click="goBack">
          <uni-icons type="arrowleft" size="20" color="#333"></uni-icons>
        </view>
        <text class="header-title">选择位置</text>
        <view class="confirm-btn" @click="confirmLocation" :class="{ disabled: !selectedLocation }">
          <text class="confirm-text">确定</text>
        </view>
      </view>
    </view>

    <!-- 搜索栏 -->
    <view class="search-section">
      <view class="search-bar">
        <uni-icons type="search" size="18" color="#999"></uni-icons>
        <input 
          class="search-input" 
          type="text" 
          placeholder="搜索地点、小区、地标" 
          v-model="searchKeyword"
          @input="onSearchInput"
          @confirm="searchLocation"
        />
        <view v-if="searchKeyword" class="clear-btn" @click="clearSearch">
          <uni-icons type="clear" size="16" color="#999"></uni-icons>
        </view>
      </view>
    </view>

    <!-- 地图容器 -->
    <view class="map-container">
      <map
        id="locationMap"
        class="map"
        :longitude="mapCenter.longitude"
        :latitude="mapCenter.latitude"
        :scale="mapScale"
        :markers="markers"
        :show-location="true"
        @markertap="onMarkerTap"
        @tap="onMapTap"
        @regionchange="onRegionChange"
      >
        <!-- 中心点标记 -->
        <cover-view class="map-center-marker">
          <cover-image src="/static/map-marker.png" class="marker-icon"></cover-image>
        </cover-view>
        
        <!-- 定位按钮 -->
        <cover-view class="location-btn" @tap="getCurrentLocation">
          <cover-image src="/static/location-icon.png" class="location-icon"></cover-image>
        </cover-view>
      </map>
    </view>

    <!-- 位置信息面板 -->
    <view class="location-panel">
      <view class="panel-header">
        <text class="panel-title">当前位置</text>
        <view class="location-status" :class="{ active: isLocating }">
          <text class="status-text">{{ isLocating ? '定位中...' : '已定位' }}</text>
        </view>
      </view>

      <view class="location-info" v-if="selectedLocation">
        <view class="location-main">
          <text class="location-name">{{ selectedLocation.name || '未知位置' }}</text>
          <text class="location-address">{{ selectedLocation.address || '获取地址中...' }}</text>
        </view>
        <view class="location-coords">
          <text class="coords-text">
            {{ selectedLocation.latitude?.toFixed(6) }}, {{ selectedLocation.longitude?.toFixed(6) }}
          </text>
        </view>
      </view>

      <!-- 搜索结果列表 -->
      <scroll-view 
        v-if="searchResults.length > 0" 
        class="search-results" 
        scroll-y
        :style="{ maxHeight: '400rpx' }"
      >
        <view 
          class="search-item" 
          v-for="(item, index) in searchResults" 
          :key="index"
          @click="selectSearchResult(item)"
        >
          <view class="search-item-content">
            <text class="search-item-name">{{ item.name }}</text>
            <text class="search-item-address">{{ item.address }}</text>
          </view>
          <view class="search-item-distance" v-if="item.distance">
            <text class="distance-text">{{ formatDistance(item.distance) }}</text>
          </view>
        </view>
      </scroll-view>

      <!-- 附近地点 -->
      <view v-else-if="nearbyPlaces.length > 0" class="nearby-section">
        <view class="nearby-header">
          <text class="nearby-title">附近地点</text>
        </view>
        <scroll-view class="nearby-list" scroll-y :style="{ maxHeight: '300rpx' }">
          <view 
            class="nearby-item" 
            v-for="(place, index) in nearbyPlaces" 
            :key="index"
            @click="selectNearbyPlace(place)"
          >
            <view class="nearby-icon">
              <text class="icon-text">{{ getPlaceIcon(place.type) }}</text>
            </view>
            <view class="nearby-content">
              <text class="nearby-name">{{ place.name }}</text>
              <text class="nearby-type">{{ place.type }}</text>
            </view>
            <view class="nearby-distance">
              <text class="distance-text">{{ formatDistance(place.distance) }}</text>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      statusBarHeight: 0,
      mapCenter: {
        longitude: 116.397470,
        latitude: 39.908823
      },
      mapScale: 16,
      markers: [],
      selectedLocation: null,
      isLocating: false,
      searchKeyword: '',
      searchResults: [],
      nearbyPlaces: [],
      searchTimer: null,
      mapContext: null
    }
  },
  
  onLoad(options) {
    // 获取系统信息
    const systemInfo = uni.getSystemInfoSync()
    this.statusBarHeight = systemInfo.statusBarHeight || 44
    
    // 创建地图上下文
    this.mapContext = uni.createMapContext('locationMap', this)
    
    // 获取当前位置
    this.getCurrentLocation()
    
    // 如果有传入的位置信息，则显示
    if (options.latitude && options.longitude) {
      this.mapCenter = {
        latitude: parseFloat(options.latitude),
        longitude: parseFloat(options.longitude)
      }
      this.selectedLocation = {
        latitude: parseFloat(options.latitude),
        longitude: parseFloat(options.longitude),
        address: options.address || '',
        name: options.name || ''
      }
    }
  },
  
  methods: {
    // 获取当前位置
    getCurrentLocation() {
      this.isLocating = true
      
      uni.getLocation({
        type: 'gcj02',
        success: (res) => {
          console.log('获取位置成功:', res)
          this.mapCenter = {
            latitude: res.latitude,
            longitude: res.longitude
          }
          this.selectedLocation = {
            latitude: res.latitude,
            longitude: res.longitude,
            address: '获取地址中...',
            name: '当前位置'
          }
          
          // 获取地址信息
          this.getAddressFromCoords(res.latitude, res.longitude)
          // 获取附近地点
          this.getNearbyPlaces(res.latitude, res.longitude)
        },
        fail: (error) => {
          console.error('获取位置失败:', error)
          uni.showToast({
            title: '获取位置失败',
            icon: 'none'
          })
        },
        complete: () => {
          this.isLocating = false
        }
      })
    },
    
    // 地图点击事件
    onMapTap(e) {
      const { latitude, longitude } = e.detail
      this.selectedLocation = {
        latitude,
        longitude,
        address: '获取地址中...',
        name: '选中位置'
      }
      
      // 获取地址信息
      this.getAddressFromCoords(latitude, longitude)
    },
    
    // 地图区域变化
    onRegionChange(e) {
      if (e.type === 'end') {
        this.mapContext.getCenterLocation({
          success: (res) => {
            this.selectedLocation = {
              latitude: res.latitude,
              longitude: res.longitude,
              address: '获取地址中...',
              name: '地图中心'
            }
            this.getAddressFromCoords(res.latitude, res.longitude)
          }
        })
      }
    },
    
    // 根据坐标获取地址
    getAddressFromCoords(latitude, longitude) {
      // 这里应该调用地图服务API获取地址
      // 暂时使用模拟数据
      setTimeout(() => {
        this.selectedLocation.address = `北京市朝阳区某某街道${Math.floor(Math.random() * 100)}号`
        this.selectedLocation.name = `位置点${Math.floor(Math.random() * 1000)}`
      }, 1000)
    },
    
    // 搜索输入
    onSearchInput() {
      if (this.searchTimer) {
        clearTimeout(this.searchTimer)
      }
      
      this.searchTimer = setTimeout(() => {
        if (this.searchKeyword.trim()) {
          this.searchLocation()
        } else {
          this.searchResults = []
        }
      }, 500)
    },
    
    // 搜索位置
    searchLocation() {
      if (!this.searchKeyword.trim()) return
      
      // 模拟搜索结果
      this.searchResults = [
        {
          name: `${this.searchKeyword}相关地点1`,
          address: '北京市朝阳区某某街道1号',
          latitude: 39.908823 + Math.random() * 0.01,
          longitude: 116.397470 + Math.random() * 0.01,
          distance: Math.floor(Math.random() * 1000)
        },
        {
          name: `${this.searchKeyword}相关地点2`,
          address: '北京市朝阳区某某街道2号',
          latitude: 39.908823 + Math.random() * 0.01,
          longitude: 116.397470 + Math.random() * 0.01,
          distance: Math.floor(Math.random() * 1000)
        }
      ]
    },
    
    // 选择搜索结果
    selectSearchResult(item) {
      this.selectedLocation = item
      this.mapCenter = {
        latitude: item.latitude,
        longitude: item.longitude
      }
      this.searchResults = []
      this.searchKeyword = ''
    },
    
    // 获取附近地点
    getNearbyPlaces(latitude, longitude) {
      // 模拟附近地点数据
      this.nearbyPlaces = [
        { name: '某某小区', type: '住宅区', distance: 50 },
        { name: '某某商场', type: '购物中心', distance: 120 },
        { name: '某某地铁站', type: '地铁站', distance: 200 },
        { name: '某某医院', type: '医院', distance: 300 },
        { name: '某某学校', type: '学校', distance: 450 }
      ]
    },
    
    // 选择附近地点
    selectNearbyPlace(place) {
      this.selectedLocation = {
        ...place,
        latitude: this.mapCenter.latitude + (Math.random() - 0.5) * 0.001,
        longitude: this.mapCenter.longitude + (Math.random() - 0.5) * 0.001,
        address: `${place.name}附近`
      }
    },
    
    // 获取地点图标
    getPlaceIcon(type) {
      const icons = {
        '住宅区': '🏠',
        '购物中心': '🛒',
        '地铁站': '🚇',
        '医院': '🏥',
        '学校': '🏫'
      }
      return icons[type] || '📍'
    },
    
    // 格式化距离
    formatDistance(distance) {
      if (distance < 1000) {
        return `${distance}m`
      } else {
        return `${(distance / 1000).toFixed(1)}km`
      }
    },
    
    // 清除搜索
    clearSearch() {
      this.searchKeyword = ''
      this.searchResults = []
    },
    
    // 确认位置
    confirmLocation() {
      if (!this.selectedLocation) {
        uni.showToast({
          title: '请选择位置',
          icon: 'none'
        })
        return
      }
      
      // 返回选中的位置信息
      const pages = getCurrentPages()
      const prevPage = pages[pages.length - 2]
      
      if (prevPage) {
        // 调用上一页的回调方法
        if (prevPage.onLocationSelected) {
          prevPage.onLocationSelected(this.selectedLocation)
        }
      }
      
      uni.navigateBack()
    },
    
    // 返回
    goBack() {
      uni.navigateBack()
    }
  }
}
</script>

<style scoped>
.location-picker-container {
  height: 100vh;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
}

/* 头部导航 */
.header {
  background: #fff;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
  z-index: 10;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
}

.back-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f8f9fa;
}

.header-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.confirm-btn {
  padding: 12rpx 24rpx;
  background: #007aff;
  border-radius: 20rpx;
  transition: all 0.3s ease;
}

.confirm-btn.disabled {
  background: #ccc;
  opacity: 0.6;
}

.confirm-text {
  font-size: 26rpx;
  color: #fff;
  font-weight: 500;
}

/* 搜索栏 */
.search-section {
  background: #fff;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.search-bar {
  background: #f8f9fa;
  border-radius: 25rpx;
  padding: 20rpx 30rpx;
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.clear-btn {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 地图容器 */
.map-container {
  flex: 1;
  position: relative;
}

.map {
  width: 100%;
  height: 100%;
}

.map-center-marker {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -100%);
  z-index: 5;
}

.marker-icon {
  width: 60rpx;
  height: 60rpx;
}

.location-btn {
  position: absolute;
  bottom: 40rpx;
  right: 40rpx;
  width: 80rpx;
  height: 80rpx;
  background: #fff;
  border-radius: 50%;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
}

.location-icon {
  width: 40rpx;
  height: 40rpx;
}

/* 位置信息面板 */
.location-panel {
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  padding: 30rpx;
  max-height: 50vh;
  overflow: hidden;
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.panel-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

.location-status {
  padding: 8rpx 16rpx;
  background: #f0f0f0;
  border-radius: 12rpx;
}

.location-status.active {
  background: #e3f2fd;
}

.status-text {
  font-size: 22rpx;
  color: #666;
}

.location-status.active .status-text {
  color: #007aff;
}

.location-info {
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.location-main {
  margin-bottom: 12rpx;
}

.location-name {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.location-address {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.location-coords {
  padding-top: 12rpx;
  border-top: 1rpx solid #e0e0e0;
}

.coords-text {
  font-size: 22rpx;
  color: #999;
  font-family: 'Monaco', 'Menlo', monospace;
}

/* 搜索结果 */
.search-results {
  max-height: 400rpx;
}

.search-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background 0.3s ease;
}

.search-item:active {
  background: #f8f9fa;
}

.search-item-content {
  flex: 1;
}

.search-item-name {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.search-item-address {
  display: block;
  font-size: 24rpx;
  color: #666;
}

.search-item-distance {
  margin-left: 20rpx;
}

.distance-text {
  font-size: 22rpx;
  color: #999;
}

/* 附近地点 */
.nearby-section {
  margin-top: 20rpx;
}

.nearby-header {
  margin-bottom: 15rpx;
}

.nearby-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
}

.nearby-list {
  max-height: 300rpx;
}

.nearby-item {
  display: flex;
  align-items: center;
  padding: 16rpx 20rpx;
  border-radius: 12rpx;
  margin-bottom: 8rpx;
  background: #f8f9fa;
  transition: background 0.3s ease;
}

.nearby-item:active {
  background: #e3f2fd;
}

.nearby-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  border-radius: 50%;
  margin-right: 20rpx;
}

.icon-text {
  font-size: 24rpx;
}

.nearby-content {
  flex: 1;
}

.nearby-name {
  display: block;
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 4rpx;
}

.nearby-type {
  display: block;
  font-size: 22rpx;
  color: #666;
}

.nearby-distance {
  margin-left: 20rpx;
}
</style>
