# 图片资源配置指南

## 需要的图片资源列表

### 1. 应用图标和Logo
- `logo.png` (120x120px) - 应用Logo
- `icon.png` (512x512px) - 应用图标

### 2. 默认占位图
- `default-avatar.png` (200x200px) - 默认头像
- `default-house.png` (400x300px) - 默认房源图片

### 3. 空状态图片
- `empty-default.png` (200x200px) - 通用空状态
- `empty-house.png` (200x200px) - 无房源状态
- `empty-favorite.png` (200x200px) - 无收藏状态
- `empty-search.png` (200x200px) - 搜索无结果状态

### 4. 轮播图
- `banner1.jpg` (750x300px) - 首页轮播图1
- `banner2.jpg` (750x300px) - 首页轮播图2
- `banner3.jpg` (750x300px) - 首页轮播图3

### 5. 地图标记
- `map-marker.png` (30x30px) - 地图标记图标

### 6. 测试房源图片
- `test-house.jpg` (400x300px) - 测试房源图片

## 推荐下载网站

### 免费图片资源
1. **Unsplash** (https://unsplash.com/)
   - 搜索关键词：house, apartment, room, real estate
   - 高质量房源图片

2. **Pexels** (https://www.pexels.com/)
   - 搜索关键词：home, property, interior, building
   - 免费商用图片

3. **Pixabay** (https://pixabay.com/)
   - 搜索关键词：房屋, 公寓, 室内
   - 支持中文搜索

### 图标资源
1. **Iconfont** (https://www.iconfont.cn/)
   - 搜索：房屋、地图、用户头像
   - 阿里巴巴图标库，免费使用

2. **Feather Icons** (https://feathericons.com/)
   - 简洁线性图标
   - 适合现代UI设计

## 图片规格要求

### 尺寸规范
- **Logo**: 正方形，建议120x120px以上
- **轮播图**: 宽高比2.5:1，建议750x300px
- **房源图片**: 宽高比4:3，建议400x300px
- **头像**: 正方形，建议200x200px
- **空状态图**: 正方形，建议200x200px

### 格式要求
- **照片**: JPG格式，质量80-90%
- **图标**: PNG格式，支持透明背景
- **Logo**: PNG格式，支持透明背景

### 文件大小
- 单张图片不超过500KB
- Logo和图标不超过100KB
- 轮播图不超过200KB

## 下载步骤

### 1. 访问推荐网站
```
1. 打开 https://unsplash.com/
2. 搜索 "apartment interior" 或 "modern house"
3. 选择合适的图片
4. 点击下载按钮
5. 选择适当的尺寸下载
```

### 2. 图片处理
```
1. 使用在线工具调整尺寸：
   - TinyPNG (https://tinypng.com/) - 压缩图片
   - Canva (https://www.canva.com/) - 调整尺寸
   - 稿定设计 (https://www.gaoding.com/) - 中文界面

2. 重命名文件按照列表要求
3. 放置到 static 目录下
```

## 具体图片建议

### Logo设计
- 简洁的房屋图标
- 使用蓝色或绿色主色调
- 可包含"租房"或"房屋"元素

### 轮播图内容
1. **banner1.jpg**: 现代公寓外观
2. **banner2.jpg**: 温馨室内装修
3. **banner3.jpg**: 城市建筑群

### 空状态图片
- 简洁的线条插画风格
- 浅色调，不要太突兀
- 可爱友好的设计风格

### 默认房源图片
- 通用的房间内景
- 明亮整洁的环境
- 中性色调

## 版权注意事项

### 免费使用条件
1. **Unsplash**: 完全免费，无需署名
2. **Pexels**: 完全免费，无需署名
3. **Pixabay**: 免费，建议署名

### 商用授权
- 确保选择的图片支持商业使用
- 避免使用有人物肖像的图片
- 保存下载记录和授权信息

## 图片优化建议

### 性能优化
1. 使用 WebP 格式（如果支持）
2. 实现图片懒加载
3. 提供多种尺寸版本
4. 使用 CDN 加速

### 用户体验
1. 提供占位图显示
2. 图片加载失败时的备用方案
3. 支持图片预览和缩放
4. 合理的图片压缩比例

## 快速配置脚本

创建一个简单的配置检查脚本：

```javascript
// 检查图片资源是否完整
const requiredImages = [
  'logo.png',
  'default-avatar.png', 
  'default-house.png',
  'empty-default.png',
  'empty-house.png',
  'empty-favorite.png',
  'empty-search.png',
  'banner1.jpg',
  'banner2.jpg', 
  'banner3.jpg',
  'map-marker.png',
  'test-house.jpg'
];

// 在小程序中检查图片是否存在
function checkImages() {
  requiredImages.forEach(image => {
    const path = `/static/${image}`;
    // 这里可以添加图片存在性检查逻辑
    console.log(`检查图片: ${path}`);
  });
}
```

按照这个指南下载和配置图片后，您的应用就会有完整的视觉资源了！
