// uni-id 通用工具函数
const uniID = require('uni-id-common');

/**
 * 检查用户token
 * @param {string} token - 用户token
 * @param {object} context - 云函数上下文
 * @returns {object} 检查结果
 */
async function checkToken(token, context) {
  const uniIdIns = uniID.createInstance({
    context: context
  });
  
  const payload = await uniIdIns.checkToken(token);
  
  if (payload.errCode === 0) {
    return {
      code: 0,
      uid: payload.uid,
      userInfo: payload.userInfo
    };
  } else {
    return {
      code: payload.errCode || 401,
      message: payload.errMsg || '身份验证失败'
    };
  }
}

/**
 * 用户注册
 * @param {object} userData - 用户数据
 * @param {object} context - 云函数上下文
 * @returns {object} 注册结果
 */
async function register(userData, context) {
  const uniIdIns = uniID.createInstance({
    context: context
  });
  
  const result = await uniIdIns.register(userData);
  
  if (result.errCode === 0) {
    return {
      code: 0,
      message: '注册成功',
      data: {
        uid: result.uid,
        token: result.token,
        tokenExpired: result.tokenExpired
      }
    };
  } else {
    return {
      code: result.errCode,
      message: result.errMsg || '注册失败'
    };
  }
}

/**
 * 用户登录
 * @param {object} loginData - 登录数据
 * @param {object} context - 云函数上下文
 * @returns {object} 登录结果
 */
async function login(loginData, context) {
  const uniIdIns = uniID.createInstance({
    context: context
  });
  
  const result = await uniIdIns.login(loginData);
  
  if (result.errCode === 0) {
    return {
      code: 0,
      message: '登录成功',
      data: {
        uid: result.uid,
        token: result.token,
        tokenExpired: result.tokenExpired,
        userInfo: result.userInfo
      }
    };
  } else {
    return {
      code: result.errCode,
      message: result.errMsg || '登录失败'
    };
  }
}

/**
 * 用户登出
 * @param {string} token - 用户token
 * @param {object} context - 云函数上下文
 * @returns {object} 登出结果
 */
async function logout(token, context) {
  const uniIdIns = uniID.createInstance({
    context: context
  });
  
  const result = await uniIdIns.logout(token);
  
  if (result.errCode === 0) {
    return {
      code: 0,
      message: '登出成功'
    };
  } else {
    return {
      code: result.errCode,
      message: result.errMsg || '登出失败'
    };
  }
}

module.exports = {
  checkToken,
  register,
  login,
  logout
};
