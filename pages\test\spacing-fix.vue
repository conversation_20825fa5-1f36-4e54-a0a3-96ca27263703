<template>
  <view class="test-container">
    <view class="test-header">
      <text class="test-title">顶部间距修复验证</text>
      <text class="test-subtitle">检查搜索页面和首页的顶部间距</text>
    </view>
    
    <view class="test-content">
      <!-- 修复说明 -->
      <view class="fix-section">
        <view class="fix-title">🔧 修复内容</view>
        
        <view class="fix-item">
          <view class="fix-icon">✅</view>
          <view class="fix-text">
            <text class="fix-name">搜索页面顶部间距</text>
            <text class="fix-desc">从 20rpx 增加到 60rpx，提供更舒适的视觉间距</text>
          </view>
        </view>
        
        <view class="fix-item">
          <view class="fix-icon">✅</view>
          <view class="fix-text">
            <text class="fix-name">首页搜索栏间距</text>
            <text class="fix-desc">同步调整首页搜索栏的顶部间距</text>
          </view>
        </view>
        
        <view class="fix-item">
          <view class="fix-icon">✅</view>
          <view class="fix-text">
            <text class="fix-name">底部间距优化</text>
            <text class="fix-desc">增加底部间距，避免内容过于紧凑</text>
          </view>
        </view>
      </view>
      
      <!-- 对比说明 */
      <view class="compare-section">
        <view class="compare-title">📏 间距对比</view>
        
        <view class="compare-item">
          <view class="compare-label before">修复前</view>
          <view class="compare-value">padding-top: calc(20rpx + var(--status-bar-height))</view>
        </view>
        
        <view class="compare-item">
          <view class="compare-label after">修复后</view>
          <view class="compare-value">padding-top: calc(60rpx + var(--status-bar-height))</view>
        </view>
        
        <view class="compare-note">
          <text class="note-text">增加了 40rpx 的额外间距，让搜索栏远离状态栏</text>
        </view>
      </view>
      
      <!-- 测试按钮 -->
      <view class="test-section">
        <view class="test-title">🧪 验证测试</view>
        
        <view class="test-item">
          <view class="test-info">
            <text class="test-name">搜索页面间距</text>
            <text class="test-desc">检查搜索页面的顶部间距是否合适</text>
          </view>
          <button class="test-btn" @click="testSearchSpacing">测试</button>
        </view>
        
        <view class="test-item">
          <view class="test-info">
            <text class="test-name">首页搜索栏间距</text>
            <text class="test-desc">检查首页搜索栏的顶部间距</text>
          </view>
          <button class="test-btn" @click="testIndexSpacing">测试</button>
        </view>
      </view>
      
      <!-- 预期效果 -->
      <view class="expected-section">
        <view class="expected-title">✨ 预期效果</view>
        
        <view class="expected-list">
          <text class="expected-item">• 搜索栏与状态栏有足够的间距</text>
          <text class="expected-item">• 视觉上更加舒适，不会感觉拥挤</text>
          <text class="expected-item">• 符合移动端设计规范</text>
          <text class="expected-item">• 提升整体用户体验</text>
        </view>
      </view>
      
      <!-- 操作按钮 -->
      <view class="action-section">
        <button class="action-btn primary" @click="testSearchSpacing">
          测试搜索页面
        </button>
        <button class="action-btn" @click="testIndexSpacing">
          测试首页
        </button>
        <button class="action-btn" @click="goBack">
          返回
        </button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  methods: {
    testSearchSpacing() {
      uni.showToast({
        title: '跳转到搜索页面检查间距',
        icon: 'none'
      });
      
      setTimeout(() => {
        uni.navigateTo({
          url: '/pages/search/search'
        });
      }, 1000);
    },
    
    testIndexSpacing() {
      uni.showToast({
        title: '跳转到首页检查间距',
        icon: 'none'
      });
      
      setTimeout(() => {
        uni.switchTab({
          url: '/pages/index/index'
        });
      }, 1000);
    },
    
    goBack() {
      uni.navigateBack();
    }
  }
}
</script>

<style scoped>
.test-container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  padding: 40rpx;
}

.test-header {
  text-align: center;
  margin-bottom: 60rpx;
  padding-top: 60rpx;
}

.test-title {
  display: block;
  font-size: 40rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 20rpx;
}

.test-subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.test-content {
  background: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.fix-section, .compare-section, .test-section, .expected-section {
  margin-bottom: 50rpx;
}

.fix-title, .compare-title, .test-title, .expected-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #303133;
  margin-bottom: 30rpx;
}

.fix-item {
  display: flex;
  align-items: flex-start;
  padding: 20rpx;
  background: #f0f9ff;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
  border-left: 4rpx solid #10b981;
}

.fix-icon {
  font-size: 24rpx;
  margin-right: 16rpx;
  margin-top: 4rpx;
}

.fix-text {
  flex: 1;
}

.fix-name {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8rpx;
}

.fix-desc {
  display: block;
  font-size: 24rpx;
  color: #10b981;
  line-height: 1.4;
}

.compare-item {
  display: flex;
  align-items: center;
  padding: 16rpx 20rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  margin-bottom: 12rpx;
}

.compare-label {
  width: 120rpx;
  font-size: 24rpx;
  font-weight: 600;
  padding: 8rpx 16rpx;
  border-radius: 6rpx;
  text-align: center;
  margin-right: 20rpx;
}

.compare-label.before {
  background: #fee2e2;
  color: #dc2626;
}

.compare-label.after {
  background: #dcfce7;
  color: #16a34a;
}

.compare-value {
  flex: 1;
  font-size: 24rpx;
  color: #606266;
  font-family: monospace;
}

.compare-note {
  padding: 16rpx 20rpx;
  background: #fffbeb;
  border-radius: 8rpx;
  border-left: 4rpx solid #f59e0b;
}

.note-text {
  font-size: 24rpx;
  color: #92400e;
  line-height: 1.4;
}

.test-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
}

.test-info {
  flex: 1;
}

.test-name {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8rpx;
}

.test-desc {
  display: block;
  font-size: 24rpx;
  color: #909399;
  line-height: 1.4;
}

.test-btn {
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
}

.expected-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.expected-item {
  font-size: 26rpx;
  color: #303133;
  line-height: 1.6;
  padding: 12rpx 16rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
}

.action-section {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.action-btn {
  height: 88rpx;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #f5f7fa;
  color: #606266;
}

.action-btn.primary {
  background: #007aff;
  color: #fff;
}

.action-btn:active {
  opacity: 0.8;
}
</style>
