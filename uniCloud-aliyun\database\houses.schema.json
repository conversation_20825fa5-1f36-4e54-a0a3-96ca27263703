{"bsonType": "object", "description": "房源信息表", "required": ["title", "price", "user_id", "location"], "properties": {"_id": {"description": "房源ID"}, "title": {"bsonType": "string", "description": "房源标题", "label": "标题", "minLength": 5, "maxLength": 100}, "description": {"bsonType": "string", "description": "房源描述", "label": "描述", "maxLength": 2000}, "type": {"bsonType": "string", "description": "房源类型", "label": "类型", "enum": ["whole", "shared", "single"], "enumDesc": ["整租", "合租", "单间"]}, "price": {"bsonType": "number", "description": "租金价格（元/月）", "label": "租金", "minimum": 0}, "deposit": {"bsonType": "number", "description": "押金（元）", "label": "押金", "minimum": 0}, "area": {"bsonType": "number", "description": "房屋面积（平方米）", "label": "面积", "minimum": 0}, "room_count": {"bsonType": "int", "description": "房间数量", "label": "房间数", "minimum": 1}, "hall_count": {"bsonType": "int", "description": "客厅数量", "label": "客厅数", "minimum": 0}, "bathroom_count": {"bsonType": "int", "description": "卫生间数量", "label": "卫生间数", "minimum": 1}, "floor": {"bsonType": "int", "description": "楼层", "label": "楼层", "minimum": 1}, "total_floors": {"bsonType": "int", "description": "总楼层", "label": "总楼层", "minimum": 1}, "orientation": {"bsonType": "string", "description": "朝向", "label": "朝向", "enum": ["东", "南", "西", "北", "东南", "东北", "西南", "西北", "南北"]}, "decoration": {"bsonType": "string", "description": "装修情况", "label": "装修", "enum": ["毛坯", "简装", "精装", "豪装"]}, "location": {"bsonType": "object", "description": "位置信息", "label": "位置", "required": ["province", "city", "district", "address"], "properties": {"province": {"bsonType": "string", "description": "省份", "label": "省份"}, "city": {"bsonType": "string", "description": "城市", "label": "城市"}, "district": {"bsonType": "string", "description": "区县", "label": "区县"}, "address": {"bsonType": "string", "description": "详细地址", "label": "详细地址"}, "longitude": {"bsonType": "number", "description": "经度", "label": "经度"}, "latitude": {"bsonType": "number", "description": "纬度", "label": "纬度"}}}, "images": {"bsonType": "array", "description": "房源图片", "label": "图片", "items": {"bsonType": "string"}}, "facilities": {"bsonType": "array", "description": "配套设施", "label": "设施", "items": {"bsonType": "string"}}, "contact": {"bsonType": "object", "description": "联系方式", "label": "联系方式", "properties": {"name": {"bsonType": "string", "description": "联系人姓名", "label": "联系人"}, "phone": {"bsonType": "string", "description": "联系电话", "label": "电话"}, "wechat": {"bsonType": "string", "description": "微信号", "label": "微信"}}}, "user_id": {"bsonType": "string", "description": "发布用户ID", "label": "发布用户", "foreignKey": "uni-id-users._id"}, "status": {"bsonType": "int", "description": "房源状态：0待审核 1已发布 2已下架 3审核拒绝", "label": "状态", "enum": [0, 1, 2, 3], "default": 1}, "view_count": {"bsonType": "int", "description": "浏览次数", "label": "浏览次数", "default": 0}, "favorite_count": {"bsonType": "int", "description": "收藏次数", "label": "收藏次数", "default": 0}, "is_featured": {"bsonType": "bool", "description": "是否推荐", "label": "推荐", "default": false}, "create_time": {"bsonType": "timestamp", "description": "创建时间", "label": "创建时间", "forceDefaultValue": {"$env": "now"}}, "update_time": {"bsonType": "timestamp", "description": "更新时间", "label": "更新时间", "forceDefaultValue": {"$env": "now"}}}}