'use strict';

exports.main = async (event, context) => {
  console.log('绕过登录验证，直接生成token...');
  
  try {
    const db = uniCloud.database();
    
    // 1. 获取admin用户
    const adminUser = await db.collection('uni-id-users').where({
      username: 'admin'
    }).get();
    
    if (adminUser.data.length === 0) {
      return {
        code: -1,
        message: 'admin用户不存在'
      };
    }
    
    const user = adminUser.data[0];
    console.log('找到admin用户:', user.username);
    
    // 2. 生成一个简单的token
    const crypto = require('crypto');
    const token = crypto.randomBytes(32).toString('hex');
    
    // 3. 创建登录会话记录
    const sessionData = {
      uid: user._id,
      username: user.username,
      nickname: user.nickname,
      role: user.role,
      token: token,
      create_time: new Date(),
      expire_time: new Date(Date.now() + 7200000), // 2小时后过期
      platform: 'web'
    };
    
    // 4. 保存会话到数据库（如果有会话表的话）
    try {
      await db.collection('uni-id-sessions').add(sessionData);
    } catch (sessionError) {
      console.log('保存会话失败（可能没有会话表）:', sessionError.message);
    }
    
    // 5. 更新用户最后登录时间
    await db.collection('uni-id-users').doc(user._id).update({
      last_login_date: new Date(),
      last_login_ip: '127.0.0.1'
    });
    
    console.log('绕过登录成功，生成token:', token);
    
    return {
      code: 0,
      message: '绕过登录成功',
      data: {
        token: token,
        uid: user._id,
        userInfo: {
          uid: user._id,
          username: user.username,
          nickname: user.nickname,
          role: user.role
        },
        tokenExpired: sessionData.expire_time,
        instructions: [
          '1. 复制上面的token',
          '2. 在浏览器开发者工具中打开Application/Storage',
          '3. 在localStorage中添加键值对：',
          '   键: uni_id_token',
          '   值: ' + token,
          '4. 刷新页面，应该可以直接进入管理后台'
        ]
      }
    };
    
  } catch (error) {
    console.error('绕过登录失败:', error);
    return {
      code: -1,
      message: '绕过登录失败',
      error: error.message
    };
  }
};
