// 应用配置文件

// 应用基础配置
export const APP_CONFIG = {
  name: '毕业租房信息平台',
  version: '1.0.0',
  description: '专为毕业生打造的租房信息平台'
};

// 房源类型配置
export const HOUSE_TYPES = [
  { value: 'whole', label: '整租' },
  { value: 'shared', label: '合租' },
  { value: 'single', label: '单间' }
];

// 房源状态配置
export const HOUSE_STATUS = {
  available: '可租',
  rented: '已租',
  offline: '下线'
};

// 预约状态配置
export const APPOINTMENT_STATUS = {
  pending: '待确认',
  confirmed: '已确认',
  cancelled: '已取消',
  completed: '已完成'
};

// 用户角色配置
export const USER_ROLES = {
  user: '用户',
  admin: '管理员'
};

// 用户身份配置（用于身份认证）
export const USER_IDENTITIES = {
  student: '学生',
  landlord: '房东'
};

// 装修情况配置
export const DECORATION_TYPES = [
  '毛坯',
  '简装',
  '精装修',
  '豪华装修'
];

// 朝向配置
export const ORIENTATIONS = [
  '东',
  '南',
  '西',
  '北',
  '东南',
  '东北',
  '西南',
  '西北',
  '南北',
  '东西'
];

// 房屋设施配置
export const FACILITIES = [
  { value: 'air_conditioner', label: '空调', icon: '❄️' },
  { value: 'washing_machine', label: '洗衣机', icon: '🧺' },
  { value: 'refrigerator', label: '冰箱', icon: '🧊' },
  { value: 'water_heater', label: '热水器', icon: '🚿' },
  { value: 'tv', label: '电视', icon: '📺' },
  { value: 'wifi', label: 'WiFi', icon: '📶' },
  { value: 'wardrobe', label: '衣柜', icon: '👗' },
  { value: 'bed', label: '床', icon: '🛏️' },
  { value: 'desk', label: '书桌', icon: '📚' },
  { value: 'sofa', label: '沙发', icon: '🛋️' },
  { value: 'microwave', label: '微波炉', icon: '🔥' },
  { value: 'gas_stove', label: '燃气灶', icon: '🍳' },
  { value: 'range_hood', label: '油烟机', icon: '💨' },
  { value: 'balcony', label: '阳台', icon: '🌿' },
  { value: 'parking', label: '停车位', icon: '🚗' },
  { value: 'elevator', label: '电梯', icon: '🛗' }
];

// 价格区间配置
export const PRICE_RANGES = [
  { min: 0, max: 1000, label: '1000元以下' },
  { min: 1000, max: 2000, label: '1000-2000元' },
  { min: 2000, max: 3000, label: '2000-3000元' },
  { min: 3000, max: 5000, label: '3000-5000元' },
  { min: 5000, max: 8000, label: '5000-8000元' },
  { min: 8000, max: 0, label: '8000元以上' }
];

// 排序方式配置
export const SORT_OPTIONS = [
  { value: 'publish_date_desc', label: '最新发布' },
  { value: 'price_asc', label: '价格从低到高' },
  { value: 'price_desc', label: '价格从高到低' },
  { value: 'view_count_desc', label: '浏览量最多' },
  { value: 'favorite_count_desc', label: '收藏量最多' }
];

// 消息类型配置
export const MESSAGE_TYPES = {
  system: '系统消息',
  appointment: '预约消息',
  contact: '联系消息'
};

// 举报原因配置
export const REPORT_REASONS = [
  '虚假房源信息',
  '价格欺诈',
  '图片与实际不符',
  '联系方式无效',
  '重复发布',
  '违法违规内容',
  '其他'
];

// 图片上传配置
export const UPLOAD_CONFIG = {
  maxSize: 5 * 1024 * 1024, // 5MB
  maxCount: 9, // 最多9张图片
  allowedTypes: ['jpg', 'jpeg', 'png', 'webp']
};

// 分页配置
export const PAGE_CONFIG = {
  pageSize: 10,
  maxPageSize: 50
};

// 地图配置
export const MAP_CONFIG = {
  defaultCenter: {
    latitude: 39.908823,
    longitude: 116.397470
  },
  defaultZoom: 12
};

// 缓存键名配置
export const STORAGE_KEYS = {
  TOKEN: 'uni_id_token',
  USER_INFO: 'userInfo',
  SEARCH_HISTORY: 'searchHistory',
  LOCATION: 'userLocation'
};

// API错误码配置
export const ERROR_CODES = {
  SUCCESS: 0,
  INVALID_PARAM: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  SERVER_ERROR: 500,
  
  // 业务错误码
  USER_NOT_EXIST: 1001,
  HOUSE_NOT_EXIST: 1002,
  ALREADY_FAVORITED: 1003,
  APPOINTMENT_CONFLICT: 1004,
  INSUFFICIENT_PERMISSION: 1005
};

// 正则表达式配置
export const REGEX = {
  phone: /^1[3-9]\d{9}$/,
  email: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
  idCard: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
  password: /^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{6,20}$/
};
