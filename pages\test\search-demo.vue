<template>
  <view class="demo-container">
    <view class="demo-header">
      <text class="demo-title">搜索功能演示</text>
      <text class="demo-subtitle">体验现代化的搜索界面设计</text>
    </view>
    
    <view class="demo-content">
      <!-- 功能介绍 -->
      <view class="feature-section">
        <view class="feature-title">🔍 搜索功能特色</view>
        
        <view class="feature-list">
          <view class="feature-item">
            <view class="feature-icon">✨</view>
            <view class="feature-text">
              <text class="feature-name">现代化设计</text>
              <text class="feature-desc">渐变背景、毛玻璃效果、流畅动画</text>
            </view>
          </view>
          
          <view class="feature-item">
            <view class="feature-icon">🎯</view>
            <view class="feature-text">
              <text class="feature-name">智能搜索</text>
              <text class="feature-desc">实时搜索建议、历史记录管理</text>
            </view>
          </view>
          
          <view class="feature-item">
            <view class="feature-icon">📱</view>
            <view class="feature-text">
              <text class="feature-name">交互优化</text>
              <text class="feature-desc">触摸反馈、动画过渡、用户友好</text>
            </view>
          </view>
          
          <view class="feature-item">
            <view class="feature-icon">🏠</view>
            <view class="feature-text">
              <text class="feature-name">房源搜索</text>
              <text class="feature-desc">支持地址、关键词、房源类型搜索</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 搜索演示 -->
      <view class="search-demo">
        <view class="demo-search-bar" @click="goToSearch">
          <uni-icons type="search" size="18" color="#999"></uni-icons>
          <text class="demo-placeholder">点击体验搜索功能</text>
          <view class="demo-arrow">
            <uni-icons type="arrowright" size="16" color="#007aff"></uni-icons>
          </view>
        </view>
      </view>
      
      <!-- 操作按钮 -->
      <view class="demo-actions">
        <button class="demo-btn primary" @click="goToSearch">
          体验搜索功能
        </button>
        <button class="demo-btn" @click="goToIndex">
          返回首页
        </button>
        <button class="demo-btn" @click="goBack">
          返回
        </button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  methods: {
    goToSearch() {
      uni.navigateTo({
        url: '/pages/search/search'
      });
    },
    
    goToIndex() {
      uni.switchTab({
        url: '/pages/index/index'
      });
    },
    
    goBack() {
      uni.navigateBack();
    }
  }
}
</script>

<style scoped>
.demo-container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  padding: 40rpx;
}

.demo-header {
  text-align: center;
  margin-bottom: 60rpx;
  padding-top: 60rpx;
}

.demo-title {
  display: block;
  font-size: 40rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 20rpx;
}

.demo-subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.demo-content {
  background: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.feature-section {
  margin-bottom: 60rpx;
}

.feature-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #303133;
  margin-bottom: 30rpx;
}

.feature-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.feature-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
  margin-top: 4rpx;
}

.feature-text {
  flex: 1;
}

.feature-name {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8rpx;
}

.feature-desc {
  display: block;
  font-size: 24rpx;
  color: #909399;
  line-height: 1.5;
}

.search-demo {
  margin-bottom: 60rpx;
}

.demo-search-bar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 30rpx;
  padding: 24rpx 30rpx;
  display: flex;
  align-items: center;
  gap: 15rpx;
  box-shadow: 0 4rpx 20rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.demo-search-bar:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 10rpx rgba(102, 126, 234, 0.4);
}

.demo-placeholder {
  flex: 1;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 400;
}

.demo-arrow {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.demo-actions {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.demo-btn {
  height: 88rpx;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #f5f7fa;
  color: #606266;
  transition: all 0.3s ease;
}

.demo-btn.primary {
  background: #007aff;
  color: #fff;
}

.demo-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}
</style>
