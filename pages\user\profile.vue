<template>
  <view class="profile-container">
    <!-- 用户信息头部 - 已登录状态 -->
    <view class="user-header" v-if="userInfo._id">
      <view class="user-info">
        <image
          class="avatar"
          :src="userInfo.avatar || '/static/default-avatar.png'"
          mode="aspectFill"
          @click="chooseAvatar"
        ></image>
        <view class="user-details">
          <text class="nickname">{{ userInfo.nickname || userInfo.username || '未设置昵称' }}</text>
          <text class="role">{{ getRoleText(userInfo.role) }}</text>
          <text class="phone">{{ userInfo.mobile || '未绑定手机' }}</text>
        </view>
      </view>
      <view class="edit-btn" @click="toEditProfile">
        <uni-icons type="compose" size="20" color="#fff"></uni-icons>
      </view>
    </view>

    <!-- 未登录状态 -->
    <view class="login-section" v-else>
      <view class="login-header">
        <view class="login-content">
          <image class="default-avatar" src="/static/default-avatar.png" mode="aspectFill"></image>
          <text class="login-tip">欢迎来到毕业租房平台</text>
          <text class="login-subtitle">登录后享受更多便捷服务</text>
          <button class="login-btn" @click="toLogin">立即登录</button>
          <view class="register-tip">
            <text class="tip-text">还没有账号？</text>
            <text class="register-link" @click="toRegister">立即注册</text>
          </view>
        </view>
      </view>

      <!-- 未登录时的功能预览 -->
      <view class="preview-section">
        <view class="section-title">
          <uni-icons type="star" size="16" color="#007aff"></uni-icons>
          <text class="title-text">登录后可享受</text>
        </view>
        <view class="preview-list">
          <view class="preview-item">
            <view class="preview-icon">
              <uni-icons type="home" size="24" color="#007aff"></uni-icons>
            </view>
            <view class="preview-content">
              <text class="preview-title">发布房源</text>
              <text class="preview-desc">免费发布房源信息，快速找到租客</text>
            </view>
          </view>
          <view class="preview-item">
            <view class="preview-icon">
              <uni-icons type="heart" size="24" color="#ff6b6b"></uni-icons>
            </view>
            <view class="preview-content">
              <text class="preview-title">收藏房源</text>
              <text class="preview-desc">收藏心仪房源，随时查看对比</text>
            </view>
          </view>
          <view class="preview-item">
            <view class="preview-icon">
              <uni-icons type="calendar" size="24" color="#ffa500"></uni-icons>
            </view>
            <view class="preview-content">
              <text class="preview-title">预约看房</text>
              <text class="preview-desc">在线预约看房时间，高效找房</text>
            </view>
          </view>
          <view class="preview-item">
            <view class="preview-icon">
              <uni-icons type="chatbubble" size="24" color="#52c41a"></uni-icons>
            </view>
            <view class="preview-content">
              <text class="preview-title">在线沟通</text>
              <text class="preview-desc">与房东直接沟通，了解详细信息</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 数据统计卡片 - 已登录状态 -->
    <view class="stats-section" v-if="userInfo._id">
      <view class="stats-grid">
        <view class="stats-item" @click="toMyHouses">
          <text class="stats-number">{{ myHousesCount }}</text>
          <text class="stats-label">我的发布</text>
        </view>
        <view class="stats-item" @click="toFavorites">
          <text class="stats-number">{{ favoritesCount }}</text>
          <text class="stats-label">我的收藏</text>
        </view>
        <view class="stats-item" @click="toAppointments">
          <text class="stats-number">{{ appointmentsCount }}</text>
          <text class="stats-label">看房记录</text>
        </view>
        <view class="stats-item" @click="toMessages">
          <text class="stats-number">{{ unreadCount }}</text>
          <text class="stats-label">未读消息</text>
        </view>
      </view>
    </view>

    <!-- 功能菜单 -->
    <view class="menu-section" v-if="userInfo._id">
      <view class="section-title">
        <uni-icons type="grid" size="16" color="#007aff"></uni-icons>
        <text class="title-text">我的服务</text>
      </view>
      <view class="menu-list">
        <view class="menu-item" @click="toMyHouses">
          <view class="menu-icon">
            <uni-icons type="home" size="24" color="#007aff"></uni-icons>
          </view>
          <text class="menu-text">我的发布</text>
          <view class="menu-badge" v-if="myHousesCount > 0">{{ myHousesCount }}</view>
          <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
        </view>
        
        <view class="menu-item" @click="toFavorites">
          <view class="menu-icon">
            <uni-icons type="heart" size="24" color="#ff6b6b"></uni-icons>
          </view>
          <text class="menu-text">我的收藏</text>
          <view class="menu-badge" v-if="favoritesCount > 0">{{ favoritesCount }}</view>
          <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
        </view>
        
        <view class="menu-item" @click="toAppointments">
          <view class="menu-icon">
            <uni-icons type="calendar" size="24" color="#ffa500"></uni-icons>
          </view>
          <text class="menu-text">看房记录</text>
          <view class="menu-badge" v-if="appointmentsCount > 0">{{ appointmentsCount }}</view>
          <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
        </view>
        
        <view class="menu-item" @click="toMessages">
          <view class="menu-icon">
            <uni-icons type="chatbubble" size="24" color="#10c560"></uni-icons>
          </view>
          <text class="menu-text">消息通知</text>
          <view class="menu-badge" v-if="unreadCount > 0">{{ unreadCount }}</view>
          <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
        </view>
      </view>
    </view>
    
    <!-- 其他功能 -->
    <view class="menu-section" v-if="userInfo._id">
      <view class="menu-title">其他功能</view>
      <view class="menu-list">
        <view class="menu-item" @click="toSettings">
          <view class="menu-icon">
            <uni-icons type="gear" size="24" color="#666"></uni-icons>
          </view>
          <text class="menu-text">设置</text>
          <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
        </view>
        
        <view class="menu-item" @click="toHelp">
          <view class="menu-icon">
            <uni-icons type="help" size="24" color="#666"></uni-icons>
          </view>
          <text class="menu-text">帮助与反馈</text>
          <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
        </view>
        
        <view class="menu-item" @click="toAbout">
          <view class="menu-icon">
            <uni-icons type="info" size="24" color="#666"></uni-icons>
          </view>
          <text class="menu-text">关于我们</text>
          <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
        </view>
      </view>
    </view>
    
    <!-- 退出登录 -->
    <view class="logout-section" v-if="userInfo._id">
      <button class="logout-btn" @click="handleLogout">退出登录</button>
    </view>
  </view>
</template>

<script>
import request from '@/utils/request.js'
import { getUserInfo, setUserInfo, clearUserInfo, checkLogin, requireLogin } from '@/utils/common.js'
import { USER_ROLES } from '@/common/config.js'

export default {
  data() {
    return {
      userInfo: {},
      myHousesCount: 0,
      favoritesCount: 0,
      appointmentsCount: 0,
      unreadCount: 0
    }
  },
  methods: {
    // 获取角色文本
    getRoleText(roles) {
      if (!roles || !Array.isArray(roles)) return '普通用户'
      const role = roles[0]
      return USER_ROLES[role] || '普通用户'
    },
    
    // 加载用户信息
    async loadUserInfo() {
      if (!checkLogin()) {
        this.userInfo = {}
        return
      }

      try {
        // 先从本地存储获取用户信息
        const localUserInfo = getUserInfo()
        if (localUserInfo && localUserInfo._id) {
          this.userInfo = localUserInfo
        }

        // 然后从服务器获取最新信息
        const result = await request.callFunction('user-auth', {
          action: 'getUserInfo'
        })

        if (result.code === 0) {
          this.userInfo = result.data
          // 更新本地存储
          setUserInfo(result.data)
        } else {
          // 如果服务器返回错误，清除本地信息
          clearUserInfo()
          this.userInfo = {}
        }
      } catch (error) {
        console.error('获取用户信息失败:', error)
        // 发生错误时，使用本地存储的信息
        const localUserInfo = getUserInfo()
        if (localUserInfo && localUserInfo._id) {
          this.userInfo = localUserInfo
        }
      }
    },
    
    // 加载统计数据
    async loadStatistics() {
      if (!checkLogin()) return

      try {
        // 显示统一的加载状态
        uni.showLoading({
          title: '加载统计数据...',
          mask: true
        });

        // 并发请求各种统计数据，禁用单个请求的loading
        const promises = [
          // 我的发布房源数量
          request.callFunction('house-management', {
            action: 'getMyHouses',
            data: { page: 1, pageSize: 1, countOnly: true }
          }, { showLoading: false, showError: false }),
          // 我的收藏数量
          request.callFunction('favorite-management', {
            action: 'getFavorites',
            data: { page: 1, pageSize: 1, countOnly: true }
          }, { showLoading: false, showError: false }),
          // 看房记录数量
          request.callFunction('appointment-management', {
            action: 'getMyAppointments',
            data: { page: 1, pageSize: 1, countOnly: true }
          }, { showLoading: false, showError: false }),
          // 未读消息数量
          request.callFunction('message-management', {
            action: 'getUnreadCount'
          }, { showLoading: false, showError: false })
        ]

        const results = await Promise.allSettled(promises)

        // 处理我的发布数量
        if (results[0].status === 'fulfilled' && results[0].value.code === 0) {
          this.myHousesCount = results[0].value.data.total || 0
        } else {
          this.myHousesCount = 0
        }

        // 处理我的收藏数量
        if (results[1].status === 'fulfilled' && results[1].value.code === 0) {
          this.favoritesCount = results[1].value.data.total || 0
        } else {
          this.favoritesCount = 0
        }

        // 处理看房记录数量
        if (results[2].status === 'fulfilled' && results[2].value.code === 0) {
          this.appointmentsCount = results[2].value.data.total || 0
        } else {
          this.appointmentsCount = 0
        }

        // 处理未读消息数量
        if (results[3].status === 'fulfilled' && results[3].value.code === 0) {
          this.unreadCount = results[3].value.data.total || 0
        } else {
          this.unreadCount = 0
        }

        // 隐藏加载状态
        uni.hideLoading()

      } catch (error) {
        console.error('获取统计数据失败:', error)

        // 隐藏加载状态
        uni.hideLoading()

        // 发生错误时重置为0
        this.myHousesCount = 0
        this.favoritesCount = 0
        this.appointmentsCount = 0
        this.unreadCount = 0

        // 显示错误提示
        uni.showToast({
          title: '获取统计数据失败',
          icon: 'none'
        })
      }
    },


    
    // 选择头像
    chooseAvatar() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: async (res) => {
          const tempFilePath = res.tempFilePaths[0]

          uni.showLoading({
            title: '上传中...'
          })

          try {
            // 上传到云存储
            const uploadResult = await request.callFunction('file-upload', {
              action: 'uploadImage',
              data: {
                filePath: tempFilePath,
                cloudPath: `avatars/${Date.now()}_${Math.random().toString(36).substr(2, 9)}.jpg`
              }
            })

            if (uploadResult.code === 0) {
              // 更新用户头像
              const updateResult = await request.callFunction('user-auth', {
                action: 'updateProfile',
                data: {
                  avatar: uploadResult.data.fileID
                }
              })

              if (updateResult.code === 0) {
                this.userInfo.avatar = uploadResult.data.fileID
                uni.showToast({
                  title: '头像更新成功',
                  icon: 'success'
                })
              }
            }
          } catch (error) {
            console.error('上传头像失败:', error)
            uni.showToast({
              title: '上传失败',
              icon: 'none'
            })
          } finally {
            uni.hideLoading()
          }
        }
      })
    },
    
    // 跳转到编辑资料页
    toEditProfile() {
      uni.navigateTo({
        url: '/pages/user/edit-profile'
      })
    },
    
    // 跳转到登录页
    toLogin() {
      requireLogin('/pages/user/profile')
    },
    
    // 跳转到我的发布
    toMyHouses() {
      uni.navigateTo({
        url: '/pages/user/my-houses'
      })
    },
    
    // 跳转到我的收藏
    toFavorites() {
      uni.navigateTo({
        url: '/pages/user/favorites'
      })
    },
    
    // 跳转到看房记录
    toAppointments() {
      uni.navigateTo({
        url: '/pages/user/appointments'
      })
    },
    
    // 跳转到消息通知
    toMessages() {
      uni.navigateTo({
        url: '/pages/user/messages'
      })
    },
    
    // 跳转到设置页
    toSettings() {
      uni.showToast({
        title: '功能开发中',
        icon: 'none'
      })
    },
    
    // 跳转到帮助页
    toHelp() {
      uni.showToast({
        title: '功能开发中',
        icon: 'none'
      })
    },
    
    // 跳转到关于页
    toAbout() {
      uni.showToast({
        title: '功能开发中',
        icon: 'none'
      })
    },

    // 跳转到注册页
    toRegister() {
      uni.navigateTo({
        url: '/pages/register/register'
      })
    },

    // 跳转到发布房源页
    toPublishHouse() {
      uni.navigateTo({
        url: '/pages/house/publish'
      })
    },

    // 跳转到身份认证页
    toIdentityVerify() {
      uni.showToast({
        title: '功能开发中',
        icon: 'none'
      })
    },

    // 获取认证状态文本
    getVerifyStatusText() {
      if (!this.userInfo.student_info && !this.userInfo.landlord_info) {
        return '未认证'
      }

      const studentVerified = this.userInfo.student_info?.verified
      const landlordVerified = this.userInfo.landlord_info?.verified

      if (studentVerified || landlordVerified) {
        return '已认证'
      } else {
        return '待审核'
      }
    },

    // 获取认证状态样式类
    getVerifyStatusClass() {
      const status = this.getVerifyStatusText()
      return {
        'status-verified': status === '已认证',
        'status-pending': status === '待审核',
        'status-unverified': status === '未认证'
      }
    },
    
    // 退出登录
    handleLogout() {
      uni.showModal({
        title: '提示',
        content: '确定要退出登录吗？',
        success: (res) => {
          if (res.confirm) {
            clearUserInfo()
            this.userInfo = {}
            uni.showToast({
              title: '已退出登录',
              icon: 'success'
            })
          }
        }
      })
    }
  },
  
  onLoad() {
    this.loadUserInfo()
    this.loadStatistics()
  },
  
  onShow() {
    // 页面显示时重新加载数据
    this.loadUserInfo()
    this.loadStatistics()
  },
  
  // 下拉刷新
  onPullDownRefresh() {
    Promise.all([
      this.loadUserInfo(),
      this.loadStatistics()
    ]).finally(() => {
      uni.stopPullDownRefresh()
    })
  }
}
</script>

<style scoped>
.profile-container {
  min-height: 100vh;
  background: #f8f9fa;
}

.user-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60rpx 40rpx 40rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.user-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.edit-btn {
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-right: 30rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.user-details {
  flex: 1;
}

.nickname {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 10rpx;
}

.role {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  background: rgba(255, 255, 255, 0.2);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  margin-bottom: 10rpx;
  width: fit-content;
}

.phone {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
}

.edit-btn {
  padding: 20rpx;
}

/* 未登录状态样式 */
.login-section {
  background: #fff;
}

.login-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-content {
  padding: 80rpx 40rpx 60rpx;
  text-align: center;
  color: #fff;
}

.default-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-bottom: 30rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.login-tip {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.login-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
  margin-bottom: 40rpx;
}

.register-tip {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10rpx;
}

.tip-text {
  font-size: 26rpx;
  opacity: 0.8;
}

.register-link {
  font-size: 26rpx;
  color: #fff;
  text-decoration: underline;
}

.login-btn {
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 50rpx;
  padding: 20rpx 60rpx;
  font-size: 32rpx;
  margin-bottom: 30rpx;
}

.login-btn:active {
  background: rgba(255, 255, 255, 0.3);
}

.menu-section {
  margin: 20rpx;
  background: #fff;
  border-radius: 20rpx;
  overflow: hidden;
}

.menu-title {
  padding: 30rpx 30rpx 20rpx;
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}

.menu-list {
  padding: 0 30rpx;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-icon {
  margin-right: 30rpx;
}

.menu-text {
  flex: 1;
  font-size: 32rpx;
  color: #333;
}

.menu-badge {
  background: #ff4757;
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 10rpx;
  margin-right: 20rpx;
  min-width: 32rpx;
  text-align: center;
}

.logout-section {
  margin: 40rpx 20rpx;
}

.logout-btn {
  width: 100%;
  background: #fff;
  color: #ff4757;
  border: 2rpx solid #ff4757;
  border-radius: 20rpx;
  padding: 30rpx;
  font-size: 32rpx;
}

/* 数据统计样式 */
.stats-section {
  background: #fff;
  margin: 20rpx 30rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}

.stats-item {
  text-align: center;
  padding: 20rpx 10rpx;
  border-radius: 12rpx;
  background: #f8f9fa;
  transition: all 0.3s ease;
}

.stats-item:active {
  background: #e9ecef;
  transform: scale(0.95);
}

.stats-number {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #007aff;
  margin-bottom: 8rpx;
}

.stats-label {
  display: block;
  font-size: 24rpx;
  color: #666;
}

/* 功能预览样式 */
.preview-section {
  padding: 40rpx 30rpx;
  background: #fff;
}

.preview-list {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.preview-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 30rpx;
  background: #f8f9fa;
  border-radius: 20rpx;
}

.preview-icon {
  width: 60rpx;
  height: 60rpx;
  background: rgba(0, 122, 255, 0.1);
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-content {
  flex: 1;
}

.preview-title {
  display: block;
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.preview-desc {
  display: block;
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

/* 认证状态样式 */
.verify-status {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  margin-left: auto;
}

.status-verified {
  background: #f6ffed;
  color: #52c41a;
}

.status-pending {
  background: #fff7e6;
  color: #fa8c16;
}

.status-unverified {
  background: #f5f5f5;
  color: #999;
}

/* 工具菜单样式 */
.tools-section {
  margin-top: 20rpx;
}
</style>
