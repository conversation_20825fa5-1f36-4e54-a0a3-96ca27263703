# uni-id 配置指南

## 🚨 错误解决

如果遇到 `Invalid uni-id config file` 错误，请按以下步骤操作：

### 1. 检查配置文件

确保以下文件存在且配置正确：
```
uniCloud-aliyun/cloudfunctions/common/uni-config-center/uni-id/config.json
```

### 2. 配置文件内容

配置文件应包含以下内容：

```json
{
  "passwordSecret": "graduation-rental-platform-password-secret-2024",
  "tokenSecret": "graduation-rental-platform-token-secret-2024",
  "tokenExpiresIn": 7200,
  "tokenExpiresThreshold": 600,
  "passwordErrorLimit": 6,
  "passwordErrorRetryTime": 3600,
  "autoSetInviteCode": false,
  "forceInviteCode": false,
  "bindTokenToDevice": false,
  "removePermissionAndRoleFromToken": false,
  "app": {
    "oauth": {
      "weixin": {
        "appid": "",
        "appsecret": ""
      }
    }
  }
}
```

### 3. 上传配置文件

1. 在 HBuilderX 中右键 `common` 目录
2. 选择"上传公共模块"
3. 等待上传完成

### 4. 重新上传云函数

1. 右键 `user-auth` 云函数目录
2. 选择"上传并运行"
3. 等待上传完成

### 5. 测试配置

在浏览器控制台中运行：
```javascript
// 测试注册
uniCloud.callFunction({
  name: 'user-auth',
  data: {
    action: 'register',
    data: {
      username: 'test123',
      password: '123456',
      mobile: '13800138000',
      role: 'student'
    }
  }
}).then(res => {
  console.log('注册结果:', res)
})
```

## 📋 完整配置步骤

### 步骤 1: 创建配置目录

确保目录结构如下：
```
uniCloud-aliyun/
├── cloudfunctions/
│   ├── common/
│   │   └── uni-config-center/
│   │       └── uni-id/
│   │           └── config.json
│   ├── user-auth/
│   ├── house-management/
│   └── ...
└── database/
    └── db_init.json
```

### 步骤 2: 配置 uni-id

1. **创建配置文件**
   - 文件路径：`uniCloud-aliyun/cloudfunctions/common/uni-config-center/uni-id/config.json`
   - 使用上面提供的配置内容

2. **修改密钥（重要）**
   - `passwordSecret`: 用于密码加密的密钥
   - `tokenSecret`: 用于 token 签名的密钥
   - 建议使用复杂的随机字符串

### 步骤 3: 上传配置

1. **上传公共模块**
   ```
   右键 common 目录 → 上传公共模块
   ```

2. **上传云函数**
   ```
   右键各个云函数目录 → 上传并运行
   ```

### 步骤 4: 初始化数据库

1. **创建数据库集合**
   - 在 uniCloud 控制台创建以下集合：
     - `uni-id-users` (用户表)
     - `houses` (房源表)
     - `favorites` (收藏表)
     - `appointments` (预约表)
     - `messages` (消息表)
     - `system_config` (系统配置表)
     - `reports` (举报表)

2. **导入初始数据**
   - 使用 `uniCloud-aliyun/database/db_init.json` 初始化数据

## 🔧 常见问题

### Q1: 配置文件不生效
**解决方案：**
1. 检查文件路径是否正确
2. 确保 JSON 格式正确（无语法错误）
3. 重新上传公共模块
4. 重新上传云函数

### Q2: 密钥配置错误
**解决方案：**
1. 确保 `passwordSecret` 和 `tokenSecret` 不为空
2. 使用足够复杂的密钥（建议 32 位以上）
3. 避免使用特殊字符

### Q3: 权限配置问题
**解决方案：**
1. 检查数据库权限设置
2. 确保云函数有读写数据库的权限
3. 检查用户角色配置

## 🧪 测试验证

### 快速测试
```javascript
// 在浏览器控制台运行
import { testUniIdConfig } from '@/test/uni-id-test.js'
testUniIdConfig()
```

### 完整测试
```javascript
// 在浏览器控制台运行
import { runFullTest } from '@/test/uni-id-test.js'
runFullTest()
```

## 📞 获取帮助

如果仍然遇到问题：

1. **检查云函数日志**
   - 在 uniCloud 控制台查看云函数执行日志
   - 查找具体的错误信息

2. **检查网络连接**
   - 确保网络连接正常
   - 检查是否有防火墙阻止

3. **重新创建项目**
   - 如果问题持续存在，考虑重新创建 uniCloud 项目
   - 重新关联云服务空间

## ✅ 配置完成检查清单

- [ ] 配置文件已创建并上传
- [ ] 公共模块已上传
- [ ] 所有云函数已上传
- [ ] 数据库集合已创建
- [ ] 测试注册功能正常
- [ ] 测试登录功能正常

完成以上步骤后，uni-id 应该可以正常工作了！
