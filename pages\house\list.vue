<template>
  <view class="house-list-container">
    <!-- 筛选栏 -->
    <view class="filter-bar">
      <view class="filter-item" @click="toggleTypeFilter" :class="{ active: showTypeOptions }">
        <text class="filter-text">{{ selectedType.label }}</text>
        <uni-icons :type="showTypeOptions ? 'arrowup' : 'arrowdown'" size="12" color="#666"></uni-icons>
      </view>
      <view class="filter-item" @click="togglePriceFilter" :class="{ active: showPriceOptions }">
        <text class="filter-text">{{ selectedPrice.label }}</text>
        <uni-icons :type="showPriceOptions ? 'arrowup' : 'arrowdown'" size="12" color="#666"></uni-icons>
      </view>
      <view class="filter-item" @click="toggleSortFilter" :class="{ active: showSortOptions }">
        <text class="filter-text">{{ selectedSort.label }}</text>
        <uni-icons :type="showSortOptions ? 'arrowup' : 'arrowdown'" size="12" color="#666"></uni-icons>
      </view>
      <view class="filter-item" @click="toSearch">
        <uni-icons type="search" size="16" color="#666"></uni-icons>
      </view>
    </view>

    <!-- 筛选选项展开区域 -->
    <view class="filter-options-container" v-if="showTypeOptions || showPriceOptions || showSortOptions">
      <!-- 房源类型选项 -->
      <view class="filter-options" v-if="showTypeOptions">
        <view class="options-title">房源类型</view>
        <view class="options-grid">
          <view
            class="option-item"
            v-for="option in typeOptions"
            :key="option.value"
            :class="{ selected: selectedType.value === option.value }"
            @click="selectType(option)"
          >
            <text class="option-text">{{ option.label }}</text>
          </view>
        </view>
      </view>

      <!-- 价格区间选项 -->
      <view class="filter-options" v-if="showPriceOptions">
        <view class="options-title">价格区间</view>
        <view class="price-input-container">
          <view class="price-input-group">
            <text class="input-label">最低价</text>
            <input
              class="price-input"
              type="number"
              placeholder="不限"
              v-model="customPrice.min"
              @input="onPriceInput"
            />
            <text class="price-unit">元/月</text>
          </view>
          <view class="price-separator">-</view>
          <view class="price-input-group">
            <text class="input-label">最高价</text>
            <input
              class="price-input"
              type="number"
              placeholder="不限"
              v-model="customPrice.max"
              @input="onPriceInput"
            />
            <text class="price-unit">元/月</text>
          </view>
        </view>
        <view class="price-actions">
          <button class="price-btn reset-btn" @click="resetPrice">重置</button>
          <button class="price-btn confirm-btn" @click="confirmPrice">确定</button>
        </view>
      </view>

      <!-- 排序方式选项 -->
      <view class="filter-options" v-if="showSortOptions">
        <view class="options-title">排序方式</view>
        <view class="options-list">
          <view
            class="option-item-list"
            v-for="option in sortOptions"
            :key="option.value"
            :class="{ selected: selectedSort.value === option.value }"
            @click="selectSort(option)"
          >
            <text class="option-text">{{ option.label }}</text>
            <uni-icons v-if="selectedSort.value === option.value" type="checkmarkempty" size="16" color="#007aff"></uni-icons>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 房源列表 -->
    <scroll-view
      class="house-list"
      scroll-y
      @scrolltolower="loadMore"
      refresher-enabled
      @refresherrefresh="onRefresh"
      :refresher-triggered="refreshing"
      @click="closeAllFilters"
    >
      <view class="house-item" v-for="house in houseList" :key="house._id" @click="toHouseDetail(house._id)">
        <view class="house-image">
          <image
            :src="house.images && house.images[0] || '/static/default-house.png'"
            mode="aspectFill"
          ></image>
          <view class="house-type">{{ getTypeText(house.type) }}</view>
          <view class="favorite-btn" @click.stop="toggleFavorite(house)">
            <uni-icons type="heart" size="20" color="#fff"></uni-icons>
          </view>
        </view>
        <view class="house-info">
          <text class="house-title">{{ house.title }}</text>
          <text class="house-desc">{{ house.description }}</text>
          <view class="house-tags">
            <text class="tag" v-if="house.room_count">{{ house.room_count }}室</text>
            <text class="tag" v-if="house.hall_count">{{ house.hall_count }}厅</text>
            <text class="tag" v-if="house.area">{{ house.area }}㎡</text>
            <text class="tag" v-if="house.orientation">{{ house.orientation }}</text>
          </view>
          <view class="house-location">
            <uni-icons type="location" size="12" color="#999"></uni-icons>
            <text class="location-text">{{ house.location.district }} {{ house.location.address }}</text>
          </view>
          <view class="house-bottom">
            <view class="price-info">
              <text class="price">¥{{ house.price }}</text>
              <text class="price-unit">/月</text>
            </view>
            <view class="house-stats">
              <text class="stat-item">
                <uni-icons type="eye" size="12" color="#999"></uni-icons>
                {{ house.view_count || 0 }}
              </text>
              <text class="stat-item">
                <uni-icons type="heart" size="12" color="#999"></uni-icons>
                {{ house.favorite_count || 0 }}
              </text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 加载状态 -->
      <view class="load-status" v-if="houseList.length > 0">
        <text v-if="loading">加载中...</text>
        <text v-else-if="noMore">没有更多了</text>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" v-if="!loading && houseList.length === 0">
        <image src="/static/empty-house.png" mode="aspectFit"></image>
        <text class="empty-text">暂无房源信息</text>
      </view>
    </scroll-view>
    
    <!-- 发布按钮 -->
    <view class="publish-btn" @click="toPublish" v-if="canPublish">
      <uni-icons type="plus" size="24" color="#fff"></uni-icons>
    </view>
    

  </view>
</template>

<script>
import request from '@/utils/request.js'
import { checkLogin } from '@/utils/common.js'
import { HOUSE_TYPES, SORT_OPTIONS } from '@/common/config.js'

export default {
  data() {
    return {
      houseList: [],
      loading: false,
      refreshing: false,
      noMore: false,
      page: 1,
      pageSize: 10,
      
      // 筛选条件
      selectedType: { value: '', label: '房源类型' },
      selectedPrice: { value: '', label: '价格区间' },
      selectedSort: { value: 'publish_date_desc', label: '最新发布' },

      // 筛选选项展开状态
      showTypeOptions: false,
      showPriceOptions: false,
      showSortOptions: false,

      // 自定义价格区间
      customPrice: {
        min: '',
        max: ''
      },

      // 筛选选项
      typeOptions: [
        { value: '', label: '全部类型' },
        ...HOUSE_TYPES
      ],
      sortOptions: SORT_OPTIONS
    }
  },
  computed: {
    canPublish() {
      return checkLogin()
    }
  },
  methods: {
    // 获取房源类型文本
    getTypeText(type) {
      const typeItem = HOUSE_TYPES.find(item => item.value === type)
      return typeItem ? typeItem.label : type
    },
    
    // 加载房源列表
    async loadHouseList(refresh = false) {
      if (this.loading) return
      
      this.loading = true
      
      if (refresh) {
        this.page = 1
        this.noMore = false
      }
      
      try {
        const params = {
          action: 'getHouseList',
          data: {
            page: this.page,
            pageSize: this.pageSize,
            sort: this.selectedSort.value
          }
        }
        
        // 添加筛选条件
        if (this.selectedType.value) {
          params.data.type = this.selectedType.value
        }

        if (this.selectedPrice.value) {
          const [minPrice, maxPrice] = this.selectedPrice.value.split('-').map(Number)
          if (minPrice >= 0) params.data.minPrice = minPrice
          if (maxPrice > 0 && maxPrice < 999999) params.data.maxPrice = maxPrice
        }

        const result = await request.callFunction('house-management', params)

        if (result.code === 0) {
          const { list, total } = result.data

          if (refresh) {
            this.houseList = list
          } else {
            this.houseList.push(...list)
          }

          this.page++
          this.noMore = this.houseList.length >= total
        } else {
          console.error('房源列表请求失败:', result)
        }
      } catch (error) {
        console.error('加载房源列表失败:', error)
      } finally {
        this.loading = false
        this.refreshing = false
      }
    },
    
    // 加载更多
    loadMore() {
      if (!this.noMore && !this.loading) {
        this.loadHouseList()
      }
    },
    
    // 下拉刷新
    onRefresh() {
      this.refreshing = true
      this.loadHouseList(true)
    },
    
    // 切换类型筛选
    toggleTypeFilter() {
      this.showTypeOptions = !this.showTypeOptions
      this.showPriceOptions = false
      this.showSortOptions = false
    },

    // 切换价格筛选
    togglePriceFilter() {
      this.showPriceOptions = !this.showPriceOptions
      this.showTypeOptions = false
      this.showSortOptions = false
    },

    // 切换排序筛选
    toggleSortFilter() {
      this.showSortOptions = !this.showSortOptions
      this.showTypeOptions = false
      this.showPriceOptions = false
    },

    // 选择房源类型
    selectType(option) {
      this.selectedType = option
      this.showTypeOptions = false
      this.loadHouseList(true)
    },

    // 价格输入处理
    onPriceInput() {
      // 实时更新价格显示标签
      this.updatePriceLabel()
    },

    // 更新价格标签
    updatePriceLabel() {
      const { min, max } = this.customPrice
      if (!min && !max) {
        this.selectedPrice.label = '价格区间'
      } else if (min && max) {
        this.selectedPrice.label = `${min}-${max}元`
      } else if (min) {
        this.selectedPrice.label = `${min}元起`
      } else if (max) {
        this.selectedPrice.label = `${max}元内`
      }
    },

    // 重置价格
    resetPrice() {
      this.customPrice.min = ''
      this.customPrice.max = ''
      this.selectedPrice = { value: '', label: '价格区间' }
      this.showPriceOptions = false
      this.loadHouseList(true)
    },

    // 确认价格筛选
    confirmPrice() {
      const { min, max } = this.customPrice

      // 验证输入
      if (min && max && parseInt(min) > parseInt(max)) {
        uni.showToast({
          title: '最低价不能大于最高价',
          icon: 'none'
        })
        return
      }

      // 设置价格筛选值
      if (min || max) {
        this.selectedPrice.value = `${min || 0}-${max || 999999}`
      } else {
        this.selectedPrice.value = ''
      }

      this.updatePriceLabel()
      this.showPriceOptions = false
      this.loadHouseList(true)
    },

    // 选择排序方式
    selectSort(option) {
      this.selectedSort = option
      this.showSortOptions = false
      this.loadHouseList(true)
    },

    // 关闭所有筛选选项
    closeAllFilters() {
      this.showTypeOptions = false
      this.showPriceOptions = false
      this.showSortOptions = false
    },

    // 跳转到搜索页
    toSearch() {
      uni.navigateTo({
        url: '/pages/search/search'
      })
    },

    // 临时调试：查看所有房源
    async debugAllHouses() {
      try {
        const result = await request.callFunction('house-management', {
          action: 'debugGetAllHouses'
        })
        console.log('所有房源数据:', result)
      } catch (error) {
        console.error('调试查询失败:', error)
      }
    },
    
    // 跳转到房源详情
    toHouseDetail(houseId) {
      uni.navigateTo({
        url: `/pages/house/detail?id=${houseId}`
      })
    },
    
    // 跳转到发布页
    toPublish() {
      if (!checkLogin()) {
        uni.showToast({
          title: '请先登录',
          icon: 'none'
        })
        uni.navigateTo({
          url: '/pages/login/login'
        })
        return
      }

      uni.navigateTo({
        url: '/pages/house/publish'
      })
    },

    // 应用房源类型筛选
    applyTypeFilter(typeValue) {
      const typeOption = this.typeOptions.find(option => option.value === typeValue)
      if (typeOption) {
        this.selectedType = typeOption
        console.log('房源列表应用类型筛选:', typeOption)

        // 显示筛选成功提示
        uni.showToast({
          title: `已筛选${typeOption.label}房源`,
          icon: 'success',
          duration: 2000
        })
      } else {
        console.warn('未找到房源类型选项:', typeValue)
      }
    },

    // 应用价格筛选
    applyPriceFilter(priceValue) {
      // 这里可以根据需要实现价格筛选逻辑
      console.log('应用价格筛选:', priceValue)
    },

    // 应用排序筛选
    applySortFilter(sortValue) {
      const sortOption = this.sortOptions.find(option => option.value === sortValue)
      if (sortOption) {
        this.selectedSort = sortOption
        console.log('应用排序筛选:', sortOption)
      }
    },

    // 切换收藏状态
    toggleFavorite(house) {
      // 这里可以实现收藏功能
      uni.showToast({
        title: '收藏功能开发中',
        icon: 'none'
      })
    }
  },
  
  onLoad(options) {
    // 处理 URL 参数
    if (options.type) {
      this.applyTypeFilter(options.type)
    }

    this.debugAllHouses() // 调试：查看所有房源
    this.loadHouseList(true)
  },

  onShow() {
    // 检查是否有从首页传递的筛选参数
    const houseListFilter = uni.getStorageSync('houseListFilter')
    if (houseListFilter) {
      // 应用筛选参数
      if (houseListFilter.type) {
        this.applyTypeFilter(houseListFilter.type)
      }
      if (houseListFilter.price) {
        this.applyPriceFilter(houseListFilter.price)
      }
      if (houseListFilter.sort) {
        this.applySortFilter(houseListFilter.sort)
      }

      // 清除存储的参数，避免重复应用
      uni.removeStorageSync('houseListFilter')
      // 重新加载列表
      this.loadHouseList(true)
      return
    }

    // 从发布页返回时刷新列表
    if (this.houseList.length > 0) {
      this.loadHouseList(true)
    }
  },
  
  onPullDownRefresh() {
    this.onRefresh()
    uni.stopPullDownRefresh()
  }
}
</script>

<style scoped>
.house-list-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
  overflow-x: hidden;
}

.filter-bar {
  background: #fff;
  padding: 20rpx 16rpx;
  display: flex;
  border-bottom: 1rpx solid #e9ecef;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  position: sticky;
  top: 0;
  z-index: 100;
}

.filter-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 12rpx;
  border-radius: 12rpx;
  transition: all 0.3s ease;
  position: relative;
}

.filter-item.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}

.filter-item.active .filter-text {
  color: #fff;
}

.filter-text {
  font-size: 28rpx;
  color: #333;
  margin-right: 8rpx;
  font-weight: 500;
}

.house-list {
  flex: 1;
  padding: 20rpx 20rpx;
  box-sizing: border-box;
}

.house-item {
  background: #fff;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 6rpx 24rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  position: relative;
  width: 100%;
  box-sizing: border-box;
}

.house-item:active {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.house-image {
  position: relative;
  height: 320rpx;
  overflow: hidden;
}

.house-image image {
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease;
}

.house-item:active .house-image image {
  transform: scale(1.05);
}

.house-type {
  position: absolute;
  top: 16rpx;
  left: 16rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.favorite-btn {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  width: 48rpx;
  height: 48rpx;
  background: rgba(0, 0, 0, 0.4);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}

.favorite-btn:active {
  transform: scale(0.9);
  background: rgba(255, 107, 107, 0.8);
}

.house-info {
  padding: 20rpx;
  overflow: hidden;
  box-sizing: border-box;
}

.house-title {
  display: block;
  font-size: 34rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.4;
}

.house-desc {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 20rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.5;
}

.house-tags {
  display: flex;
  gap: 8rpx;
  margin-bottom: 16rpx;
  flex-wrap: wrap;
}

.tag {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  color: #495057;
  padding: 8rpx 12rpx;
  border-radius: 14rpx;
  font-size: 20rpx;
  font-weight: 500;
  border: 1rpx solid #e9ecef;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.tag:first-child {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  border-color: #667eea;
}

.house-location {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 10rpx 12rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
  border-left: 3rpx solid #667eea;
}

.location-text {
  font-size: 24rpx;
  color: #666;
  margin-left: 8rpx;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.house-bottom {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 16rpx;
  border-top: 1rpx solid #f0f0f0;
}

.price-info {
  display: flex;
  align-items: baseline;
}

.price {
  font-size: 40rpx;
  font-weight: 700;
  color: #667eea;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.price-unit {
  font-size: 24rpx;
  color: #999;
  margin-left: 8rpx;
  font-weight: 500;
}

.house-stats {
  display: flex;
  gap: 16rpx;
  flex-wrap: wrap;
}

.stat-item {
  font-size: 20rpx;
  color: #999;
  display: flex;
  align-items: center;
  gap: 4rpx;
  padding: 4rpx 8rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  transition: all 0.3s ease;
  flex-shrink: 0;
  white-space: nowrap;
}

.stat-item:active {
  background: #e9ecef;
}

.load-status {
  text-align: center;
  padding: 40rpx;
  color: #999;
  font-size: 28rpx;
  background: #f8f9fa;
  margin: 20rpx;
  border-radius: 16rpx;
}

.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
  background: #fff;
  margin: 20rpx;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.empty-state image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
  opacity: 0.6;
}

.empty-text {
  display: block;
  font-size: 28rpx;
  color: #999;
  margin-bottom: 12rpx;
}

.publish-btn {
  position: fixed;
  right: 40rpx;
  bottom: 120rpx;
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 12rpx 40rpx rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;
  z-index: 100;
}

.publish-btn:active {
  transform: scale(0.95) translateY(-2rpx);
  box-shadow: 0 16rpx 50rpx rgba(102, 126, 234, 0.5);
}

/* 筛选选项展开容器 */
.filter-options-container {
  background: #fff;
  border-bottom: 1rpx solid #e9ecef;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  position: relative;
  z-index: 99;
}

.filter-options {
  padding: 32rpx 24rpx;
}

.options-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 24rpx;
  position: relative;
  padding-left: 16rpx;
}

.options-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4rpx;
  height: 20rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 2rpx;
}

/* 网格布局（用于类型和价格） */
.options-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.option-item {
  flex: 1;
  min-width: 0;
  padding: 20rpx 24rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.option-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.option-item:active::before {
  left: 100%;
}

.option-item.selected {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
  color: #fff;
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.3);
}

.option-item .option-text {
  font-size: 26rpx;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.option-item.selected .option-text {
  color: #fff;
}

/* 列表布局（用于排序） */
.options-list {
  display: flex;
  flex-direction: column;
}

.option-item-list {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 25rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.option-item-list:last-child {
  border-bottom: none;
}

.option-item-list.selected {
  background: #f0f8ff;
  margin: 0 -40rpx;
  padding: 25rpx 40rpx;
  border-radius: 8rpx;
}

.option-item-list .option-text {
  font-size: 28rpx;
  color: #333;
}

.option-item-list.selected .option-text {
  color: #007aff;
  font-weight: 500;
}

/* 筛选栏激活状态 */
.filter-item.active {
  background: #f0f8ff;
}

.filter-item.active .filter-text {
  color: #007aff;
}

/* 价格输入样式 */
.price-input-container {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.price-input-group {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.input-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.price-input {
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  background: #fff;
  text-align: center;
}

.price-input:focus {
  border-color: #007aff;
}

.price-unit {
  font-size: 24rpx;
  color: #666;
  text-align: center;
  margin-top: 5rpx;
}

.price-separator {
  font-size: 32rpx;
  color: #666;
  margin-top: 30rpx;
  font-weight: bold;
}

.price-actions {
  display: flex;
  gap: 20rpx;
  margin-top: 20rpx;
}

.price-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.reset-btn {
  background: #f5f5f5;
  color: #666;
}

.confirm-btn {
  background: #007aff;
  color: #fff;
}

.price-btn:active {
  opacity: 0.8;
}
</style>
