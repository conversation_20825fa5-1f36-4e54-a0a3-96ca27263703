# 毕业租房信息平台数据库设计

## 数据库表结构设计

### 1. 用户表 (uni-id-users)
使用uniCloud内置的用户表，扩展字段：
```json
{
  "_id": "用户ID",
  "username": "用户名",
  "nickname": "昵称", 
  "avatar": "头像URL",
  "mobile": "手机号",
  "email": "邮箱",
  "gender": "性别 0-未知 1-男 2-女",
  "role": ["user", "admin"], // 角色：用户、管理员
  "current_role": "user", // 当前激活角色
  "student_info": {
    "school": "学校名称",
    "major": "专业",
    "graduation_year": "毕业年份",
    "student_id": "学号",
    "verified": false // 学生身份认证状态
  },
  "landlord_info": {
    "real_name": "真实姓名",
    "id_card": "身份证号",
    "verified": false // 房东身份认证状态
  },
  "status": 0, // 账户状态 0-正常 1-禁用
  "register_date": "注册时间",
  "last_login_date": "最后登录时间"
}
```

### 2. 房源表 (houses)
```json
{
  "_id": "房源ID",
  "title": "房源标题",
  "description": "房源描述",
  "type": "房源类型：整租、合租、单间",
  "price": 2000, // 租金（月）
  "deposit": 2000, // 押金
  "area": 80, // 面积（平方米）
  "room_count": 2, // 房间数
  "hall_count": 1, // 客厅数
  "bathroom_count": 1, // 卫生间数
  "floor": 5, // 楼层
  "total_floors": 20, // 总楼层
  "orientation": "南北", // 朝向
  "decoration": "精装修", // 装修情况
  "facilities": ["空调", "洗衣机", "冰箱", "热水器"], // 设施
  "images": ["图片URL1", "图片URL2"], // 房源图片
  "location": {
    "province": "省份",
    "city": "城市", 
    "district": "区县",
    "address": "详细地址",
    "longitude": 116.397128, // 经度
    "latitude": 39.916527 // 纬度
  },
  "contact": {
    "name": "联系人姓名",
    "phone": "联系电话",
    "wechat": "微信号"
  },
  "publisher_id": "发布者用户ID",
  "status": "available", // 状态：available-可租，rented-已租，offline-下线
  "view_count": 0, // 浏览次数
  "favorite_count": 0, // 收藏次数
  "is_verified": false, // 是否已审核
  "publish_date": "发布时间",
  "update_date": "更新时间",
  "expire_date": "过期时间"
}
```

### 3. 收藏表 (favorites)
```json
{
  "_id": "收藏ID",
  "user_id": "用户ID",
  "house_id": "房源ID",
  "create_date": "收藏时间"
}
```

### 4. 预约看房表 (appointments)
```json
{
  "_id": "预约ID",
  "user_id": "用户ID",
  "house_id": "房源ID",
  "publisher_id": "发布者ID",
  "appointment_date": "预约看房时间",
  "contact_phone": "联系电话",
  "message": "留言",
  "status": "pending", // 状态：pending-待确认，confirmed-已确认，cancelled-已取消，completed-已完成
  "create_date": "预约时间",
  "update_date": "更新时间"
}
```

### 5. 消息表 (messages)
```json
{
  "_id": "消息ID",
  "from_user_id": "发送者ID",
  "to_user_id": "接收者ID",
  "type": "消息类型：system-系统消息，appointment-预约消息，contact-联系消息",
  "title": "消息标题",
  "content": "消息内容",
  "related_id": "关联ID（如房源ID、预约ID）",
  "is_read": false, // 是否已读
  "create_date": "发送时间"
}
```

### 6. 系统配置表 (system_config)
```json
{
  "_id": "配置ID",
  "key": "配置键",
  "value": "配置值",
  "description": "配置描述",
  "update_date": "更新时间"
}
```

### 7. 举报表 (reports)
```json
{
  "_id": "举报ID",
  "reporter_id": "举报人ID",
  "target_type": "举报目标类型：house-房源，user-用户",
  "target_id": "举报目标ID",
  "reason": "举报原因",
  "description": "详细描述",
  "status": "pending", // 状态：pending-待处理，processed-已处理，rejected-已驳回
  "create_date": "举报时间",
  "process_date": "处理时间",
  "processor_id": "处理人ID"
}
```

## 数据库索引设计

### houses表索引
- location.city + location.district (地区查询)
- price (价格排序)
- publish_date (时间排序)
- publisher_id (发布者房源查询)
- status (状态筛选)

### favorites表索引
- user_id (用户收藏查询)
- house_id (房源收藏统计)

### appointments表索引
- user_id (用户预约查询)
- publisher_id (发布者预约查询)
- house_id (房源预约查询)

### messages表索引
- to_user_id + is_read (未读消息查询)
- from_user_id (发送消息查询)
