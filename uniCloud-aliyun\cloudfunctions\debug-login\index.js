'use strict';

exports.main = async (event, context) => {
  console.log('调试登录问题...');
  
  try {
    const db = uniCloud.database();
    const uniIdCo = uniCloud.importObject('uni-id-co');
    
    // 1. 检查用户是否存在
    const adminUser = await db.collection('uni-id-users').where({
      username: 'admin'
    }).get();
    
    console.log('admin用户查询结果:', adminUser.data);
    
    if (adminUser.data.length === 0) {
      return {
        code: -1,
        message: 'admin用户不存在',
        suggestion: '需要先创建admin用户'
      };
    }
    
    const user = adminUser.data[0];
    console.log('用户详情:', {
      id: user._id,
      username: user.username,
      nickname: user.nickname,
      status: user.status,
      role: user.role,
      password: user.password ? '已设置' : '未设置'
    });
    
    // 2. 尝试登录
    try {
      const loginResult = await uniIdCo.login({
        username: 'admin',
        password: 'admin123456'
      });
      
      console.log('登录结果:', loginResult);
      
      if (loginResult.errCode === 0) {
        return {
          code: 0,
          message: '登录成功',
          data: loginResult
        };
      } else {
        return {
          code: -1,
          message: '登录失败',
          error: loginResult.errMsg,
          errorCode: loginResult.errCode
        };
      }
      
    } catch (loginError) {
      console.error('登录异常:', loginError);
      return {
        code: -1,
        message: '登录异常',
        error: loginError.message
      };
    }
    
  } catch (error) {
    console.error('调试失败:', error);
    return {
      code: -1,
      message: '调试失败',
      error: error.message
    };
  }
};
