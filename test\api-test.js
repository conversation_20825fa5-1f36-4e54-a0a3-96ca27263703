// API 测试文件
// 用于测试云函数接口的基本功能

const testConfig = {
  // 测试用户信息
  testUser: {
    username: 'test_user',
    password: '123456',
    mobile: '13800138000',
    email: '<EMAIL>',
    role: 'student'
  },
  
  // 测试房源信息
  testHouse: {
    title: '测试房源',
    description: '这是一个测试房源',
    type: 'whole',
    price: 2000,
    deposit: 2000,
    area: 80,
    room_count: 2,
    hall_count: 1,
    bathroom_count: 1,
    floor: 5,
    total_floors: 20,
    orientation: '南北',
    decoration: '精装修',
    facilities: ['空调', '洗衣机', '冰箱'],
    images: ['/static/test-house.jpg'],
    location: {
      province: '北京市',
      city: '北京市',
      district: '朝阳区',
      address: '测试地址123号',
      longitude: 116.397128,
      latitude: 39.916527
    },
    contact: {
      name: '测试房东',
      phone: '13900139000',
      wechat: 'test_wechat'
    }
  }
};

// 测试用例
const testCases = {
  // 用户认证测试
  async testUserAuth() {
    console.log('=== 用户认证测试 ===');
    
    try {
      // 测试注册
      console.log('测试用户注册...');
      const registerResult = await uniCloud.callFunction({
        name: 'user-auth',
        data: {
          action: 'register',
          data: testConfig.testUser
        }
      });
      console.log('注册结果:', registerResult);
      
      // 测试登录
      console.log('测试用户登录...');
      const loginResult = await uniCloud.callFunction({
        name: 'user-auth',
        data: {
          action: 'login',
          data: {
            username: testConfig.testUser.username,
            password: testConfig.testUser.password
          }
        }
      });
      console.log('登录结果:', loginResult);
      
      if (loginResult.result.code === 0) {
        const token = loginResult.result.token;
        
        // 测试获取用户信息
        console.log('测试获取用户信息...');
        const userInfoResult = await uniCloud.callFunction({
          name: 'user-auth',
          data: {
            action: 'getUserInfo',
            uniIdToken: token
          }
        });
        console.log('用户信息结果:', userInfoResult);
        
        return token;
      }
    } catch (error) {
      console.error('用户认证测试失败:', error);
    }
  },
  
  // 房源管理测试
  async testHouseManagement(token) {
    console.log('=== 房源管理测试 ===');
    
    try {
      // 测试发布房源
      console.log('测试发布房源...');
      const publishResult = await uniCloud.callFunction({
        name: 'house-management',
        data: {
          action: 'publishHouse',
          data: testConfig.testHouse,
          uniIdToken: token
        }
      });
      console.log('发布房源结果:', publishResult);
      
      if (publishResult.result.code === 0) {
        const houseId = publishResult.result.data.house_id;
        
        // 测试获取房源详情
        console.log('测试获取房源详情...');
        const detailResult = await uniCloud.callFunction({
          name: 'house-management',
          data: {
            action: 'getHouseDetail',
            data: { house_id: houseId }
          }
        });
        console.log('房源详情结果:', detailResult);
        
        // 测试获取房源列表
        console.log('测试获取房源列表...');
        const listResult = await uniCloud.callFunction({
          name: 'house-management',
          data: {
            action: 'getHouseList',
            data: { page: 1, pageSize: 10 }
          }
        });
        console.log('房源列表结果:', listResult);
        
        return houseId;
      }
    } catch (error) {
      console.error('房源管理测试失败:', error);
    }
  },
  
  // 收藏管理测试
  async testFavoriteManagement(token, houseId) {
    console.log('=== 收藏管理测试 ===');
    
    try {
      // 测试添加收藏
      console.log('测试添加收藏...');
      const addResult = await uniCloud.callFunction({
        name: 'favorite-management',
        data: {
          action: 'addFavorite',
          data: { house_id: houseId },
          uniIdToken: token
        }
      });
      console.log('添加收藏结果:', addResult);
      
      // 测试检查收藏状态
      console.log('测试检查收藏状态...');
      const checkResult = await uniCloud.callFunction({
        name: 'favorite-management',
        data: {
          action: 'checkFavoriteStatus',
          data: { house_id: houseId },
          uniIdToken: token
        }
      });
      console.log('收藏状态结果:', checkResult);
      
      // 测试获取收藏列表
      console.log('测试获取收藏列表...');
      const listResult = await uniCloud.callFunction({
        name: 'favorite-management',
        data: {
          action: 'getFavoriteList',
          data: { page: 1, pageSize: 10 },
          uniIdToken: token
        }
      });
      console.log('收藏列表结果:', listResult);
      
    } catch (error) {
      console.error('收藏管理测试失败:', error);
    }
  },
  
  // 预约管理测试
  async testAppointmentManagement(token, houseId) {
    console.log('=== 预约管理测试 ===');
    
    try {
      // 测试创建预约
      console.log('测试创建预约...');
      const createResult = await uniCloud.callFunction({
        name: 'appointment-management',
        data: {
          action: 'createAppointment',
          data: {
            house_id: houseId,
            appointment_date: new Date(Date.now() + 24 * 60 * 60 * 1000), // 明天
            contact_phone: '13800138000',
            message: '测试预约'
          },
          uniIdToken: token
        }
      });
      console.log('创建预约结果:', createResult);
      
      // 测试获取预约列表
      console.log('测试获取预约列表...');
      const listResult = await uniCloud.callFunction({
        name: 'appointment-management',
        data: {
          action: 'getAppointmentList',
          data: { page: 1, pageSize: 10 },
          uniIdToken: token
        }
      });
      console.log('预约列表结果:', listResult);
      
    } catch (error) {
      console.error('预约管理测试失败:', error);
    }
  },
  
  // 消息管理测试
  async testMessageManagement(token) {
    console.log('=== 消息管理测试 ===');
    
    try {
      // 测试获取未读消息数量
      console.log('测试获取未读消息数量...');
      const unreadResult = await uniCloud.callFunction({
        name: 'message-management',
        data: {
          action: 'getUnreadCount',
          uniIdToken: token
        }
      });
      console.log('未读消息数量结果:', unreadResult);
      
      // 测试获取消息列表
      console.log('测试获取消息列表...');
      const listResult = await uniCloud.callFunction({
        name: 'message-management',
        data: {
          action: 'getMessageList',
          data: { page: 1, pageSize: 10 },
          uniIdToken: token
        }
      });
      console.log('消息列表结果:', listResult);
      
    } catch (error) {
      console.error('消息管理测试失败:', error);
    }
  }
};

// 运行所有测试
async function runAllTests() {
  console.log('开始运行 API 测试...');
  
  try {
    // 1. 测试用户认证
    const token = await testCases.testUserAuth();
    
    if (token) {
      // 2. 测试房源管理
      const houseId = await testCases.testHouseManagement(token);
      
      if (houseId) {
        // 3. 测试收藏管理
        await testCases.testFavoriteManagement(token, houseId);
        
        // 4. 测试预约管理
        await testCases.testAppointmentManagement(token, houseId);
      }
      
      // 5. 测试消息管理
      await testCases.testMessageManagement(token);
    }
    
    console.log('所有测试完成！');
  } catch (error) {
    console.error('测试运行失败:', error);
  }
}

// 导出测试函数
export {
  testConfig,
  testCases,
  runAllTests
};
