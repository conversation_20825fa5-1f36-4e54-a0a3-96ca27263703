'use strict';

exports.main = async (event, context) => {
  console.log('修复登录问题...');
  
  try {
    const db = uniCloud.database();
    
    // 1. 检查admin用户
    const adminUser = await db.collection('uni-id-users').where({
      username: 'admin'
    }).get();
    
    if (adminUser.data.length === 0) {
      return {
        code: -1,
        message: 'admin用户不存在'
      };
    }
    
    const user = adminUser.data[0];
    console.log('找到admin用户:', user.username);
    
    // 2. 尝试使用正确的uni-id-co登录参数
    try {
      console.log('尝试uni-id-co登录（带客户端信息）...');
      const uniIdCo = uniCloud.importObject('uni-id-co', {
        clientInfo: {
          uniPlatform: 'web',
          appId: '__UNI__080E7BD',
          deviceId: 'test-device-id',
          osName: 'web'
        }
      });
      
      const loginResult = await uniIdCo.login({
        username: 'admin',
        password: 'admin123456'
      });
      
      console.log('uni-id-co登录结果:', loginResult);
      
      if (loginResult.errCode === 0) {
        return {
          code: 0,
          message: 'uni-id-co登录成功',
          data: {
            token: loginResult.token,
            uid: loginResult.uid,
            userInfo: loginResult.userInfo
          }
        };
      } else {
        console.log('uni-id-co登录失败:', loginResult.errMsg, loginResult.errCode);
      }
      
    } catch (uniIdError) {
      console.log('uni-id-co登录异常:', uniIdError.message);
    }
    
    // 3. 尝试使用uni-id-common（旧版本）
    try {
      console.log('尝试uni-id-common登录...');
      const uniIdCommon = require('uni-id-common');
      const uniIdIns = uniIdCommon.createInstance({
        context: context
      });
      
      const loginResult = await uniIdIns.login({
        username: 'admin',
        password: 'admin123456'
      });
      
      console.log('uni-id-common登录结果:', loginResult);
      
      if (loginResult.errCode === 0) {
        return {
          code: 0,
          message: 'uni-id-common登录成功',
          data: loginResult
        };
      }
      
    } catch (commonError) {
      console.log('uni-id-common登录异常:', commonError.message);
    }
    
    // 4. 直接更新用户为明文密码（临时测试）
    console.log('设置明文密码进行测试...');
    await db.collection('uni-id-users').doc(user._id).update({
      password: 'admin123456',
      password_hash: '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi'
    });
    
    // 5. 再次尝试uni-id-co登录
    try {
      console.log('使用明文密码再次尝试登录...');
      const uniIdCo = uniCloud.importObject('uni-id-co', {
        clientInfo: {
          uniPlatform: 'web',
          appId: '__UNI__080E7BD',
          deviceId: 'test-device-id',
          osName: 'web'
        }
      });
      
      const loginResult = await uniIdCo.login({
        username: 'admin',
        password: 'admin123456'
      });
      
      console.log('明文密码登录结果:', loginResult);
      
      if (loginResult.errCode === 0) {
        return {
          code: 0,
          message: '明文密码登录成功',
          data: loginResult
        };
      } else {
        return {
          code: -1,
          message: '明文密码登录也失败',
          error: loginResult.errMsg,
          errorCode: loginResult.errCode
        };
      }
      
    } catch (finalError) {
      console.log('最终登录尝试异常:', finalError.message);
      
      return {
        code: -1,
        message: '所有登录尝试都失败',
        lastError: finalError.message,
        suggestion: '可能需要检查uni-id配置或重新安装uni-id模块'
      };
    }
    
  } catch (error) {
    console.error('修复登录失败:', error);
    return {
      code: -1,
      message: '修复失败',
      error: error.message
    };
  }
};
