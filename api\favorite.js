// 收藏相关API
import { callAPI } from './index.js'

/**
 * 添加收藏
 * @param {string} houseId 房源ID
 */
export function addFavorite(houseId) {
  return callAPI('favorite-management', 'addFavorite', { houseId })
}

/**
 * 取消收藏
 * @param {string} houseId 房源ID
 */
export function removeFavorite(houseId) {
  return callAPI('favorite-management', 'removeFavorite', { houseId })
}

/**
 * 切换收藏状态
 * @param {string} houseId 房源ID
 */
export async function toggleFavorite(houseId) {
  // 先检查收藏状态
  const statusResult = await checkFavoriteStatus(houseId)
  if (!statusResult.success) {
    return statusResult
  }
  
  const isFavorited = statusResult.data.isFavorited
  
  if (isFavorited) {
    return removeFavorite(houseId)
  } else {
    return addFavorite(houseId)
  }
}

/**
 * 获取收藏列表
 * @param {Object} params 查询参数
 * @param {number} params.page 页码
 * @param {number} params.pageSize 每页数量
 */
export function getFavoriteList(params = {}) {
  return callAPI('favorite-management', 'getFavoriteList', params)
}

/**
 * 检查收藏状态
 * @param {string} houseId 房源ID
 */
export function checkFavoriteStatus(houseId) {
  return callAPI('favorite-management', 'checkFavoriteStatus', { houseId })
}

/**
 * 批量检查收藏状态
 * @param {Array} houseIds 房源ID数组
 */
export function batchCheckFavoriteStatus(houseIds) {
  return callAPI('favorite-management', 'batchCheckFavoriteStatus', { houseIds })
}

/**
 * 获取收藏统计
 */
export function getFavoriteStats() {
  return callAPI('favorite-management', 'getFavoriteStats', {})
}

/**
 * 清空收藏列表
 */
export function clearFavorites() {
  return callAPI('favorite-management', 'clearFavorites', {})
}
