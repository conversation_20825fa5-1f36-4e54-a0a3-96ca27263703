<template>
  <view class="register-container">
    <view class="header">
      <text class="title">创建超级管理员</text>
      <text class="subtitle">首次使用需要创建管理员账号</text>
    </view>
    
    <view class="form-container">
      <view class="form-item">
        <view class="input-wrapper">
          <uni-icons type="person" size="20" color="#999"></uni-icons>
          <input 
            class="input" 
            type="text" 
            placeholder="请输入管理员用户名" 
            v-model="form.username"
            maxlength="20"
          />
        </view>
      </view>
      
      <view class="form-item">
        <view class="input-wrapper">
          <uni-icons type="phone" size="20" color="#999"></uni-icons>
          <input 
            class="input" 
            type="number" 
            placeholder="请输入手机号" 
            v-model="form.mobile"
            maxlength="11"
          />
        </view>
      </view>
      
      <view class="form-item">
        <view class="input-wrapper">
          <uni-icons type="locked" size="20" color="#999"></uni-icons>
          <input 
            class="input" 
            :type="showPassword ? 'text' : 'password'" 
            placeholder="请输入密码" 
            v-model="form.password"
            maxlength="20"
          />
          <uni-icons 
            :type="showPassword ? 'eye-slash' : 'eye'" 
            size="20" 
            color="#999"
            @click="togglePassword"
          ></uni-icons>
        </view>
      </view>
      
      <view class="form-item">
        <view class="input-wrapper">
          <uni-icons type="locked" size="20" color="#999"></uni-icons>
          <input 
            class="input" 
            :type="showConfirmPassword ? 'text' : 'password'" 
            placeholder="请确认密码" 
            v-model="form.confirmPassword"
            maxlength="20"
          />
          <uni-icons 
            :type="showConfirmPassword ? 'eye-slash' : 'eye'" 
            size="20" 
            color="#999"
            @click="toggleConfirmPassword"
          ></uni-icons>
        </view>
      </view>
      
      <button class="register-btn" @click="handleRegister" :disabled="!canRegister">
        {{ loading ? '创建中...' : '创建管理员' }}
      </button>
    </view>
    
    <view class="footer">
      <text class="tip">创建成功后将自动跳转到登录页面</text>
    </view>
  </view>
</template>

<script>
import request from '@/utils/request.js'

export default {
  data() {
    return {
      form: {
        username: '',
        mobile: '',
        password: '',
        confirmPassword: ''
      },
      showPassword: false,
      showConfirmPassword: false,
      loading: false
    }
  },
  
  computed: {
    canRegister() {
      const { username, mobile, password, confirmPassword } = this.form
      return username.trim() && 
             mobile.trim() && 
             password.trim() && 
             confirmPassword.trim() && 
             password === confirmPassword &&
             !this.loading
    }
  },
  
  methods: {
    togglePassword() {
      this.showPassword = !this.showPassword
    },
    
    toggleConfirmPassword() {
      this.showConfirmPassword = !this.showConfirmPassword
    },
    
    async handleRegister() {
      if (!this.canRegister) return
      
      const { username, mobile, password, confirmPassword } = this.form
      
      // 验证手机号格式
      const phoneReg = /^1[3-9]\d{9}$/
      if (!phoneReg.test(mobile)) {
        uni.showToast({
          title: '手机号格式不正确',
          icon: 'none'
        })
        return
      }
      
      // 验证密码长度
      if (password.length < 6) {
        uni.showToast({
          title: '密码至少6位',
          icon: 'none'
        })
        return
      }
      
      // 验证密码一致性
      if (password !== confirmPassword) {
        uni.showToast({
          title: '两次密码不一致',
          icon: 'none'
        })
        return
      }
      
      this.loading = true
      
      try {
        const result = await request.callFunction('user-auth', {
          action: 'register',
          data: {
            username: username.trim(),
            mobile: mobile.trim(),
            password: password,
            role: 'admin'
          }
        })
        
        if (result.code === 0) {
          uni.showToast({
            title: '管理员创建成功',
            icon: 'success'
          })
          
          // 延迟跳转到登录页面
          setTimeout(() => {
            uni.reLaunch({
              url: '/pages/login/login'
            })
          }, 1500)
        }
      } catch (error) {
        console.error('创建管理员失败:', error)
      } finally {
        this.loading = false
      }
    }
  },
  
  onLoad() {
    // 检查是否已有管理员
    this.checkExistingAdmin()
  },
  
  async checkExistingAdmin() {
    try {
      const db = uniCloud.database()
      const result = await db.collection('uni-id-users').where({
        role: 'admin'
      }).count()
      
      if (result.total > 0) {
        uni.showModal({
          title: '提示',
          content: '管理员已存在，请直接登录',
          showCancel: false,
          success: () => {
            uni.reLaunch({
              url: '/pages/login/login'
            })
          }
        })
      }
    } catch (error) {
      console.error('检查管理员失败:', error)
    }
  }
}
</script>

<style scoped>
.register-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60rpx 40rpx 40rpx;
  display: flex;
  flex-direction: column;
}

.header {
  text-align: center;
  margin-bottom: 80rpx;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #fff;
  display: block;
  margin-bottom: 20rpx;
}

.subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  display: block;
}

.form-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 40rpx;
}

.form-item {
  width: 100%;
}

.input-wrapper {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12rpx;
  padding: 0 30rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
  height: 100rpx;
}

.input {
  flex: 1;
  font-size: 32rpx;
  color: #333;
}

.register-btn {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  color: #fff;
  border: none;
  border-radius: 12rpx;
  height: 100rpx;
  font-size: 36rpx;
  font-weight: bold;
  margin-top: 40rpx;
}

.register-btn:disabled {
  background: #ccc;
  color: #999;
}

.footer {
  text-align: center;
  margin-top: 60rpx;
}

.tip {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
}
</style>
