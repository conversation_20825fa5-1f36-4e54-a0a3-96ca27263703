# 房源筛选功能实现说明

## 🎯 功能目标

实现从首页快捷入口点击"整租"、"合租"、"单间"时，能正确跳转到房源列表页面并自动应用对应的筛选条件。

## 🔧 技术实现

### 1. 首页快捷入口设置

**位置**: `pages/index/index.vue`

```vue
<!-- 快捷入口 -->
<view class="quick-grid">
  <view class="quick-item" @click="toHouseList('whole')">
    <text class="quick-title">整租</text>
  </view>
  <view class="quick-item" @click="toHouseList('shared')">
    <text class="quick-title">合租</text>
  </view>
  <view class="quick-item" @click="toHouseList('single')">
    <text class="quick-title">单间</text>
  </view>
</view>
```

### 2. 首页跳转方法

```javascript
// 跳转到房源列表
toHouseList(type) {
  // 房源列表是 tabBar 页面，需要使用 switchTab
  if (type) {
    // 如果有类型参数，先存储到本地，然后在房源列表页面读取
    console.log('首页设置房源筛选类型:', type)
    uni.setStorageSync('houseListFilter', { type })
    
    // 显示提示信息
    const typeNames = {
      'whole': '整租',
      'shared': '合租', 
      'single': '单间'
    }
    uni.showToast({
      title: `跳转到${typeNames[type] || type}房源`,
      icon: 'none',
      duration: 1500
    })
  }
  uni.switchTab({
    url: '/pages/house/list'
  })
}
```

### 3. 房源列表页面参数接收

**位置**: `pages/house/list.vue`

```javascript
onLoad(options) {
  // 处理 URL 参数
  if (options.type) {
    this.applyTypeFilter(options.type)
  }
  
  this.loadHouseList(true)
},

onShow() {
  // 检查是否有从首页传递的筛选参数
  const houseListFilter = uni.getStorageSync('houseListFilter')
  if (houseListFilter) {
    // 应用筛选参数
    if (houseListFilter.type) {
      this.applyTypeFilter(houseListFilter.type)
    }
    
    // 清除存储的参数，避免重复应用
    uni.removeStorageSync('houseListFilter')
    // 重新加载列表
    this.loadHouseList(true)
    return
  }
  
  // 从发布页返回时刷新列表
  if (this.houseList.length > 0) {
    this.loadHouseList(true)
  }
}
```

### 4. 筛选条件应用方法

```javascript
// 应用房源类型筛选
applyTypeFilter(typeValue) {
  const typeOption = this.typeOptions.find(option => option.value === typeValue)
  if (typeOption) {
    this.selectedType = typeOption
    console.log('房源列表应用类型筛选:', typeOption)
    
    // 显示筛选成功提示
    uni.showToast({
      title: `已筛选${typeOption.label}房源`,
      icon: 'success',
      duration: 2000
    })
  } else {
    console.warn('未找到房源类型选项:', typeValue)
  }
}
```

## 📋 房源类型配置

**位置**: `common/config.js`

```javascript
export const HOUSE_TYPES = [
  { value: 'whole', label: '整租' },
  { value: 'shared', label: '合租' },
  { value: 'single', label: '单间' }
];
```

## 🔄 数据流程

### 1. 用户点击整租按钮

```
用户点击 → toHouseList('whole') → 存储筛选参数 → 跳转页面
```

### 2. 房源列表页面接收参数

```
onShow() → 读取存储参数 → applyTypeFilter('whole') → 设置筛选条件
```

### 3. 筛选条件应用

```
selectedType = { value: 'whole', label: '整租' } → loadHouseList() → API 请求
```

### 4. API 请求参数

```javascript
const params = {
  action: 'getHouseList',
  data: {
    page: this.page,
    pageSize: this.pageSize,
    sort: this.selectedSort.value,
    type: 'whole'  // 筛选条件
  }
}
```

## 🎯 关键技术点

### 1. TabBar 页面跳转

- **问题**: tabBar 页面不能使用 `uni.navigateTo`
- **解决**: 使用 `uni.switchTab` 跳转
- **参数传递**: 通过本地存储传递参数

### 2. 参数传递策略

- **存储**: `uni.setStorageSync('houseListFilter', { type })`
- **读取**: `uni.getStorageSync('houseListFilter')`
- **清理**: `uni.removeStorageSync('houseListFilter')`

### 3. 数据结构匹配

- **首页传递**: `{ type: 'whole' }`
- **列表页接收**: `this.selectedType = { value: 'whole', label: '整租' }`
- **API 请求**: `params.data.type = 'whole'`

## ✅ 测试验证

### 1. 功能测试步骤

1. **打开首页**
2. **点击"整租"按钮**
3. **验证跳转**: 应该跳转到房源列表页面
4. **验证筛选**: 房源类型应该显示为"整租"
5. **验证数据**: 列表应该只显示整租房源

### 2. 预期结果

- ✅ 点击整租 → 跳转到房源列表
- ✅ 筛选条件显示"整租"
- ✅ 只显示 type='whole' 的房源
- ✅ 显示成功提示信息

### 3. 调试信息

- **控制台日志**: 查看筛选参数传递过程
- **Toast 提示**: 用户友好的操作反馈
- **数据验证**: 确保 API 请求参数正确

## 🐛 常见问题

### 1. 筛选不生效

**原因**: 数据结构不匹配
**解决**: 确保 `selectedType` 对象结构正确

### 2. 参数丢失

**原因**: 本地存储读取时机不对
**解决**: 在 `onShow` 中读取并立即清除

### 3. 重复应用筛选

**原因**: 参数没有及时清除
**解决**: 读取后立即 `removeStorageSync`

## 📁 相关文件

- `pages/index/index.vue` - 首页快捷入口
- `pages/house/list.vue` - 房源列表页面
- `common/config.js` - 房源类型配置
- `pages/test/house-filter-test.vue` - 功能测试页面
- `docs/house-filter-implementation.md` - 本实现文档

## 🚀 扩展功能

### 1. 价格筛选

可以类似地实现价格区间筛选：

```javascript
// 首页传递价格参数
toHouseList(type, priceRange) {
  uni.setStorageSync('houseListFilter', { type, price: priceRange })
}

// 列表页应用价格筛选
applyPriceFilter(priceValue) {
  const priceOption = this.priceOptions.find(option => option.value === priceValue)
  if (priceOption) {
    this.selectedPrice = priceOption
  }
}
```

### 2. 组合筛选

支持多个筛选条件组合：

```javascript
uni.setStorageSync('houseListFilter', {
  type: 'whole',
  price: '2000-3000',
  sort: 'price_asc'
})
```

### 3. 筛选历史

记录用户的筛选偏好：

```javascript
const filterHistory = uni.getStorageSync('filterHistory') || []
filterHistory.unshift({ type, timestamp: Date.now() })
uni.setStorageSync('filterHistory', filterHistory.slice(0, 10))
```
