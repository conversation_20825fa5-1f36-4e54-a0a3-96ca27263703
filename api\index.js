// API统一管理入口
import request from '@/utils/request.js'

/**
 * 统一的API调用封装
 * @param {string} cloudFunction 云函数名称
 * @param {string} action 操作类型
 * @param {object} data 请求数据
 * @param {boolean} needAuth 是否需要身份验证
 */
export function callAPI(cloudFunction, action, data = {}, needAuth = true) {
  return request.callFunction(cloudFunction, {
    action,
    data,
    uniIdToken: needAuth ? uni.getStorageSync('uni_id_token') : undefined
  })
}

/**
 * 处理API响应
 * @param {Promise} apiPromise API调用Promise
 * @param {string} successMessage 成功提示消息
 * @param {boolean} showSuccess 是否显示成功提示
 */
export async function handleAPIResponse(apiPromise, successMessage = '', showSuccess = false) {
  try {
    const result = await apiPromise
    
    if (result.code === 0) {
      if (showSuccess && successMessage) {
        uni.showToast({
          title: successMessage,
          icon: 'success'
        })
      }
      return {
        success: true,
        data: result.data
      }
    } else {
      // 处理错误
      const errorMessage = result.message || '操作失败'
      uni.showToast({
        title: errorMessage,
        icon: 'none'
      })
      
      // 如果是身份验证失败，跳转到登录页
      if (result.code === 401) {
        uni.removeStorageSync('uni_id_token')
        uni.removeStorageSync('userInfo')
        uni.reLaunch({
          url: '/pages/login/login'
        })
      }
      
      return {
        success: false,
        error: errorMessage,
        code: result.code
      }
    }
  } catch (error) {
    console.error('API调用异常:', error)
    uni.showToast({
      title: '网络异常，请重试',
      icon: 'none'
    })
    
    return {
      success: false,
      error: '网络异常',
      code: 500
    }
  }
}

/**
 * 分页数据处理
 * @param {object} pageData 分页数据
 * @param {array} currentList 当前列表数据
 * @param {boolean} isRefresh 是否是刷新操作
 */
export function handlePageData(pageData, currentList = [], isRefresh = false) {
  const { list = [], total = 0, page = 1, pageSize = 10 } = pageData
  
  let newList = []
  if (isRefresh || page === 1) {
    newList = list
  } else {
    newList = [...currentList, ...list]
  }
  
  const hasMore = newList.length < total
  
  return {
    list: newList,
    total,
    hasMore,
    currentPage: page
  }
}

// 导出所有API模块
export * from './user.js'
export * from './house.js'
export * from './favorite.js'
export * from './appointment.js'
export * from './message.js'
