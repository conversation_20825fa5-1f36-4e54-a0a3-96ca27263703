<template>
  <view class="edit-profile-container">
    <view class="form-container">
      <view class="form-item">
        <text class="label">头像</text>
        <view class="avatar-section" @click="chooseAvatar">
          <image 
            class="avatar" 
            :src="form.avatar || '/static/default-avatar.png'" 
            mode="aspectFill"
          ></image>
          <text class="avatar-tip">点击更换头像</text>
        </view>
      </view>
      
      <view class="form-item">
        <text class="label">昵称</text>
        <view class="input-wrapper">
          <input 
            class="input" 
            type="text" 
            placeholder="请输入昵称" 
            v-model="form.nickname"
            maxlength="20"
          />
        </view>
      </view>
      
      <view class="form-item">
        <text class="label">性别</text>
        <view class="gender-selector">
          <view 
            class="gender-item" 
            :class="{ active: form.gender === 0 }"
            @click="selectGender(0)"
          >
            <text class="gender-text">保密</text>
          </view>
          <view 
            class="gender-item" 
            :class="{ active: form.gender === 1 }"
            @click="selectGender(1)"
          >
            <text class="gender-text">男</text>
          </view>
          <view 
            class="gender-item" 
            :class="{ active: form.gender === 2 }"
            @click="selectGender(2)"
          >
            <text class="gender-text">女</text>
          </view>
        </view>
      </view>
      
      <view class="form-item">
        <text class="label">邮箱</text>
        <view class="input-wrapper">
          <input 
            class="input" 
            type="text" 
            placeholder="请输入邮箱" 
            v-model="form.email"
          />
        </view>
      </view>
      
      <!-- 学生信息 -->
      <view class="form-section" v-if="isStudent">
        <text class="section-title">学生信息</text>
        
        <view class="form-item">
          <text class="label">学校</text>
          <view class="input-wrapper">
            <input 
              class="input" 
              type="text" 
              placeholder="请输入学校名称" 
              v-model="form.student_info.school"
            />
          </view>
        </view>
        
        <view class="form-item">
          <text class="label">专业</text>
          <view class="input-wrapper">
            <input 
              class="input" 
              type="text" 
              placeholder="请输入专业" 
              v-model="form.student_info.major"
            />
          </view>
        </view>
        
        <view class="form-item">
          <text class="label">毕业年份</text>
          <view class="input-wrapper">
            <input 
              class="input" 
              type="number" 
              placeholder="请输入毕业年份" 
              v-model="form.student_info.graduation_year"
            />
          </view>
        </view>
      </view>
      
      <!-- 房东信息 -->
      <view class="form-section" v-if="isLandlord">
        <text class="section-title">房东信息</text>
        
        <view class="form-item">
          <text class="label">真实姓名</text>
          <view class="input-wrapper">
            <input 
              class="input" 
              type="text" 
              placeholder="请输入真实姓名" 
              v-model="form.landlord_info.real_name"
            />
          </view>
        </view>
        
        <view class="form-item">
          <text class="label">身份证号</text>
          <view class="input-wrapper">
            <input 
              class="input" 
              type="text" 
              placeholder="请输入身份证号" 
              v-model="form.landlord_info.id_card"
              maxlength="18"
            />
          </view>
        </view>
      </view>
      
      <button class="save-btn" @click="handleSave" :disabled="loading">
        {{ loading ? '保存中...' : '保存' }}
      </button>
    </view>
  </view>
</template>

<script>
import request from '@/utils/request.js'
import { getUserInfo, validateEmail, validateIdCard } from '@/utils/common.js'

export default {
  data() {
    return {
      form: {
        avatar: '',
        nickname: '',
        gender: 0,
        email: '',
        student_info: {
          school: '',
          major: '',
          graduation_year: ''
        },
        landlord_info: {
          real_name: '',
          id_card: ''
        }
      },
      userInfo: {},
      loading: false
    }
  },
  computed: {
    isStudent() {
      return this.userInfo.student_info && this.userInfo.student_info.verified
    },
    isLandlord() {
      return this.userInfo.landlord_info && this.userInfo.landlord_info.verified
    }
  },
  methods: {
    // 选择性别
    selectGender(gender) {
      this.form.gender = gender
    },
    
    // 选择头像
    chooseAvatar() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: async (res) => {
          const tempFilePath = res.tempFilePaths[0]
          
          uni.showLoading({
            title: '上传中...'
          })
          
          try {
            const uploadResult = await request.callFunction('file-upload', {
              action: 'uploadImage',
              data: {
                filePath: tempFilePath,
                cloudPath: `avatars/${Date.now()}_${Math.random().toString(36).substr(2, 9)}.jpg`
              }
            })
            
            if (uploadResult.code === 0) {
              this.form.avatar = uploadResult.data.fileID
              uni.showToast({
                title: '头像上传成功',
                icon: 'success'
              })
            }
          } catch (error) {
            console.error('上传头像失败:', error)
            uni.showToast({
              title: '上传失败',
              icon: 'none'
            })
          } finally {
            uni.hideLoading()
          }
        }
      })
    },
    
    // 保存资料
    async handleSave() {
      // 表单验证
      if (this.form.email && !validateEmail(this.form.email)) {
        uni.showToast({
          title: '邮箱格式不正确',
          icon: 'none'
        })
        return
      }
      
      if (this.isLandlord && this.form.landlord_info.id_card && !validateIdCard(this.form.landlord_info.id_card)) {
        uni.showToast({
          title: '身份证号格式不正确',
          icon: 'none'
        })
        return
      }
      
      this.loading = true
      
      try {
        const result = await request.callFunction('user-auth', {
          action: 'updateProfile',
          data: this.form
        })
        
        if (result.code === 0) {
          uni.showToast({
            title: '保存成功',
            icon: 'success'
          })
          
          setTimeout(() => {
            uni.navigateBack()
          }, 1500)
        }
      } catch (error) {
        console.error('保存失败:', error)
      } finally {
        this.loading = false
      }
    },
    
    // 加载用户信息
    async loadUserInfo() {
      try {
        const result = await request.callFunction('user-auth', {
          action: 'getUserInfo'
        })
        
        if (result.code === 0) {
          this.userInfo = result.data
          
          // 填充表单
          this.form = {
            avatar: result.data.avatar || '',
            nickname: result.data.nickname || '',
            gender: result.data.gender || 0,
            email: result.data.email || '',
            student_info: result.data.student_info || {
              school: '',
              major: '',
              graduation_year: ''
            },
            landlord_info: result.data.landlord_info || {
              real_name: '',
              id_card: ''
            }
          }
        }
      } catch (error) {
        console.error('获取用户信息失败:', error)
      }
    }
  },
  
  onLoad() {
    this.loadUserInfo()
  }
}
</script>

<style scoped>
.edit-profile-container {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 20rpx;
}

.form-container {
  background: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
}

.form-item {
  margin-bottom: 40rpx;
}

.label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: 500;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 0;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-bottom: 20rpx;
  border: 4rpx solid #f0f0f0;
}

.avatar-tip {
  font-size: 24rpx;
  color: #999;
}

.input-wrapper {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 0 24rpx;
  height: 88rpx;
  display: flex;
  align-items: center;
  border: 2rpx solid transparent;
}

.input-wrapper:focus-within {
  border-color: #007aff;
  background: #fff;
}

.input {
  flex: 1;
  font-size: 32rpx;
  color: #333;
}

.gender-selector {
  display: flex;
  gap: 20rpx;
}

.gender-item {
  flex: 1;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx;
  text-align: center;
  border: 2rpx solid transparent;
}

.gender-item.active {
  background: #e3f2fd;
  border-color: #007aff;
}

.gender-text {
  font-size: 28rpx;
  color: #333;
}

.form-section {
  margin-top: 60rpx;
  padding-top: 40rpx;
  border-top: 1rpx solid #f0f0f0;
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 40rpx;
}

.save-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(45deg, #007aff, #0056d3);
  color: #fff;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: bold;
  margin-top: 40rpx;
}

.save-btn:disabled {
  background: #ccc;
}
</style>
