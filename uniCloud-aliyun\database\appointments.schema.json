{"bsonType": "object", "description": "预约看房记录表", "required": ["house_id", "user_id", "appointment_date", "contact_phone"], "properties": {"_id": {"description": "预约ID"}, "house_id": {"bsonType": "string", "description": "房源ID", "label": "房源", "foreignKey": "houses._id"}, "user_id": {"bsonType": "string", "description": "预约用户ID", "label": "预约用户", "foreignKey": "uni-id-users._id"}, "appointment_date": {"bsonType": "timestamp", "description": "预约看房时间", "label": "预约时间"}, "contact_name": {"bsonType": "string", "description": "联系人姓名", "label": "联系人", "maxLength": 50}, "contact_phone": {"bsonType": "string", "description": "联系电话", "label": "联系电话", "pattern": "^\\+?[0-9-]{3,20}$"}, "message": {"bsonType": "string", "description": "预约留言", "label": "留言", "maxLength": 500}, "status": {"bsonType": "int", "description": "预约状态：0待确认 1已确认 2已完成 3已取消 4已拒绝", "label": "状态", "enum": [0, 1, 2, 3, 4], "enumDesc": ["待确认", "已确认", "已完成", "已取消", "已拒绝"], "default": 0}, "confirm_time": {"bsonType": "timestamp", "description": "确认时间", "label": "确认时间"}, "confirm_user_id": {"bsonType": "string", "description": "确认人ID（房东或管理员）", "label": "确认人", "foreignKey": "uni-id-users._id"}, "reject_reason": {"bsonType": "string", "description": "拒绝原因", "label": "拒绝原因", "maxLength": 200}, "notes": {"bsonType": "string", "description": "备注信息", "label": "备注", "maxLength": 500}, "create_time": {"bsonType": "timestamp", "description": "创建时间", "label": "创建时间", "forceDefaultValue": {"$env": "now"}}, "update_time": {"bsonType": "timestamp", "description": "更新时间", "label": "更新时间", "forceDefaultValue": {"$env": "now"}}}}