'use strict';

exports.main = async (event, context) => {
  console.log('使用uni-id-co注册管理员...');
  
  try {
    // 导入uni-id-co
    const uniIdCo = uniCloud.importObject('uni-id-co');
    
    // 先尝试注册
    const registerResult = await uniIdCo.register({
      username: 'admin',
      password: 'admin123456',
      nickname: '系统管理员'
    });
    
    console.log('注册结果:', registerResult);
    
    if (registerResult.errCode === 0) {
      // 注册成功，设置管理员角色
      const db = uniCloud.database();
      await db.collection('uni-id-users').doc(registerResult.uid).update({
        role: ['admin']
      });
      
      return {
        code: 0,
        message: '管理员注册成功',
        data: {
          username: 'admin',
          password: 'admin123456',
          uid: registerResult.uid
        }
      };
    } else if (registerResult.errCode === 'uni-id-account-exists') {
      // 用户已存在，尝试重置密码
      console.log('用户已存在，尝试重置密码...');
      
      const db = uniCloud.database();
      
      // 查找admin用户
      const adminUser = await db.collection('uni-id-users').where({
        username: 'admin'
      }).get();
      
      if (adminUser.data.length > 0) {
        const adminId = adminUser.data[0]._id;
        
        // 使用uni-id-co重置密码
        const resetResult = await uniIdCo.resetPwdByAdmin({
          uid: adminId,
          password: 'admin123456'
        });
        
        console.log('重置密码结果:', resetResult);
        
        // 确保角色正确
        await db.collection('uni-id-users').doc(adminId).update({
          role: ['admin'],
          status: 0
        });
        
        return {
          code: 0,
          message: '管理员密码重置成功',
          data: {
            username: 'admin',
            password: 'admin123456',
            uid: adminId
          }
        };
      }
    } else {
      return {
        code: -1,
        message: '注册失败',
        error: registerResult.errMsg
      };
    }
    
  } catch (error) {
    console.error('操作失败:', error);
    
    // 备用方案：直接数据库操作
    try {
      console.log('尝试备用方案...');
      const db = uniCloud.database();
      
      // 删除现有admin用户（如果存在）
      await db.collection('uni-id-users').where({
        username: 'admin'
      }).remove();
      
      // 创建新的admin用户
      const result = await db.collection('uni-id-users').add({
        username: 'admin',
        password: '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
        nickname: '系统管理员',
        role: ['admin'],
        status: 0,
        register_date: new Date(),
        register_ip: '127.0.0.1'
      });
      
      return {
        code: 0,
        message: '备用方案成功，管理员创建完成',
        data: {
          username: 'admin',
          password: 'admin123456',
          uid: result.id
        }
      };
      
    } catch (backupError) {
      return {
        code: -1,
        message: '所有方案都失败',
        error: backupError.message
      };
    }
  }
};
