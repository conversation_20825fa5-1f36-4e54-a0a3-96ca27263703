'use strict';

exports.main = async (event, context) => {
  console.log('简单直接创建管理员...');
  
  try {
    const db = uniCloud.database();
    
    // 1. 先删除所有现有的admin用户
    console.log('清理现有admin用户...');
    await db.collection('uni-id-users').where({
      username: 'admin'
    }).remove();
    
    // 2. 创建管理员角色（如果不存在）
    console.log('检查管理员角色...');
    const existingRole = await db.collection('uni-id-roles').where({
      role_id: 'admin'
    }).get();
    
    if (existingRole.data.length === 0) {
      await db.collection('uni-id-roles').add({
        role_id: 'admin',
        role_name: '系统管理员',
        permission: ['*'],
        comment: '系统管理员，拥有所有权限',
        create_date: new Date()
      });
      console.log('管理员角色创建成功');
    }
    
    // 3. 创建新的管理员用户
    console.log('创建新的管理员用户...');
    const adminData = {
      username: 'admin',
      password: '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // admin123456
      nickname: '系统管理员',
      role: ['admin'],
      status: 0,
      register_date: new Date(),
      register_ip: '127.0.0.1',
      last_login_date: new Date(),
      last_login_ip: '127.0.0.1'
    };
    
    const result = await db.collection('uni-id-users').add(adminData);
    console.log('管理员用户创建成功，ID:', result.id);
    
    // 4. 验证创建结果
    const verifyResult = await db.collection('uni-id-users').where({
      username: 'admin'
    }).get();
    
    if (verifyResult.data.length > 0) {
      const user = verifyResult.data[0];
      console.log('验证成功，用户详情:', {
        id: user._id,
        username: user.username,
        nickname: user.nickname,
        role: user.role,
        status: user.status
      });
      
      return {
        code: 0,
        message: '管理员创建成功',
        data: {
          username: 'admin',
          password: 'admin123456',
          userId: user._id,
          userDetails: {
            id: user._id,
            username: user.username,
            nickname: user.nickname,
            role: user.role,
            status: user.status
          }
        }
      };
    } else {
      return {
        code: -1,
        message: '创建后验证失败'
      };
    }
    
  } catch (error) {
    console.error('创建管理员失败:', error);
    return {
      code: -1,
      message: '创建失败',
      error: error.message
    };
  }
};
