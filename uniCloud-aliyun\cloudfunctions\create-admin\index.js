'use strict';

exports.main = async (event, context) => {
  console.log('开始创建管理员账号...');
  
  try {
    const db = uniCloud.database();
    
    // 检查是否已有admin用户
    const existingAdmin = await db.collection('uni-id-users').where({
      username: 'admin'
    }).get();
    
    if (existingAdmin.data.length > 0) {
      console.log('admin用户已存在，更新密码...');
      
      // 更新现有用户
      const adminId = existingAdmin.data[0]._id;
      await db.collection('uni-id-users').doc(adminId).update({
        password: '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
        status: 0,
        role: ['admin'],
        nickname: '系统管理员'
      });
      
      return {
        code: 0,
        message: '管理员账号已更新',
        data: {
          username: 'admin',
          password: 'admin123456',
          note: '密码已重置'
        }
      };
    }
    
    // 创建管理员角色（如果不存在）
    const existingRole = await db.collection('uni-id-roles').where({
      role_id: 'admin'
    }).get();
    
    if (existingRole.data.length === 0) {
      await db.collection('uni-id-roles').add({
        role_id: 'admin',
        role_name: '系统管理员',
        permission: ['*'],
        comment: '系统管理员，拥有所有权限',
        create_date: new Date()
      });
      console.log('管理员角色创建成功');
    }
    
    // 创建新的管理员用户
    const result = await db.collection('uni-id-users').add({
      username: 'admin',
      password: '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
      nickname: '系统管理员',
      role: ['admin'],
      status: 0,
      register_date: new Date(),
      register_ip: '127.0.0.1',
      last_login_date: new Date()
    });
    
    console.log('管理员账号创建成功，ID:', result.id);
    
    return {
      code: 0,
      message: '管理员账号创建成功',
      data: {
        username: 'admin',
        password: 'admin123456',
        userId: result.id,
        note: '请使用此账号密码登录后端管理系统'
      }
    };
    
  } catch (error) {
    console.error('创建管理员失败:', error);
    
    return {
      code: -1,
      message: '创建失败',
      error: error.message
    };
  }
};
