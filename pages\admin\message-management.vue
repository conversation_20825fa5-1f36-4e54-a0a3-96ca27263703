<template>
  <view class="admin-message-management">
    <!-- 页面标题 -->
    <uni-card title="消息管理" is-full>
      <!-- 操作按钮 -->
      <view class="action-section">
        <button class="uni-button" type="primary" @click="showSendModal = true">
          发送系统消息
        </button>
        <button class="uni-button" @click="showBatchSendModal = true">
          批量发送消息
        </button>
      </view>

      <!-- 搜索和筛选 -->
      <view class="search-section">
        <uni-row :gutter="20">
          <uni-col :span="6">
            <uni-easyinput 
              v-model="searchForm.keyword" 
              placeholder="搜索消息内容/发送人"
              @confirm="handleSearch"
            />
          </uni-col>
          <uni-col :span="4">
            <uni-data-select
              v-model="searchForm.type"
              :localdata="typeOptions"
              placeholder="消息类型"
              @change="handleSearch"
            />
          </uni-col>
          <uni-col :span="4">
            <uni-data-select
              v-model="searchForm.status"
              :localdata="statusOptions"
              placeholder="消息状态"
              @change="handleSearch"
            />
          </uni-col>
          <uni-col :span="4">
            <button class="uni-button" type="primary" @click="handleSearch">搜索</button>
          </uni-col>
          <uni-col :span="6">
            <button class="uni-button" @click="resetSearch">重置</button>
          </uni-col>
        </uni-row>
      </view>

      <!-- 消息列表 -->
      <uni-table 
        ref="table" 
        :loading="loading" 
        border 
        stripe
      >
        <uni-tr>
          <uni-th align="center">消息类型</uni-th>
          <uni-th align="center">标题</uni-th>
          <uni-th align="center">内容</uni-th>
          <uni-th align="center">发送人</uni-th>
          <uni-th align="center">接收人</uni-th>
          <uni-th align="center">状态</uni-th>
          <uni-th align="center">发送时间</uni-th>
          <uni-th align="center">操作</uni-th>
        </uni-tr>
        <uni-tr v-for="(item, index) in messageList" :key="index">
          <uni-td align="center">
            <uni-tag 
              :text="getTypeText(item.type)"
              :type="getTypeColor(item.type)"
              size="mini"
            />
          </uni-td>
          <uni-td align="center">{{ item.title }}</uni-td>
          <uni-td align="center">
            <view class="message-content">{{ item.content }}</view>
          </uni-td>
          <uni-td align="center">{{ item.sender?.nickname || '系统' }}</uni-td>
          <uni-td align="center">{{ item.receiver?.nickname || '全体用户' }}</uni-td>
          <uni-td align="center">
            <uni-tag 
              :text="getStatusText(item.status)"
              :type="getStatusType(item.status)"
            />
          </uni-td>
          <uni-td align="center">{{ formatDate(item.send_date) }}</uni-td>
          <uni-td align="center">
            <view class="action-buttons">
              <button 
                class="uni-button uni-button--mini" 
                type="primary" 
                @click="viewMessage(item)"
              >
                查看
              </button>
              <button 
                class="uni-button uni-button--mini" 
                type="warn"
                @click="deleteMessage(item)"
                v-if="item.type === 'system'"
              >
                删除
              </button>
            </view>
          </uni-td>
        </uni-tr>
      </uni-table>

      <!-- 分页 -->
      <view class="pagination-section">
        <uni-pagination 
          :current="pagination.page"
          :total="pagination.total"
          :page-size="pagination.pageSize"
          @change="handlePageChange"
        />
      </view>
    </uni-card>

    <!-- 发送消息弹窗 -->
    <uni-popup ref="sendPopup" v-model="showSendModal" type="dialog">
      <uni-popup-dialog 
        title="发送系统消息"
        @confirm="sendMessage"
        @close="showSendModal = false"
      >
        <view class="send-form">
          <uni-forms ref="sendForm" :model="sendForm" :rules="sendRules">
            <uni-forms-item label="消息标题" name="title" required>
              <uni-easyinput v-model="sendForm.title" placeholder="请输入消息标题" />
            </uni-forms-item>
            <uni-forms-item label="消息内容" name="content" required>
              <textarea 
                v-model="sendForm.content" 
                placeholder="请输入消息内容"
                class="message-textarea"
              />
            </uni-forms-item>
            <uni-forms-item label="接收人" name="receiverType" required>
              <uni-data-select
                v-model="sendForm.receiverType"
                :localdata="receiverOptions"
                placeholder="请选择接收人类型"
              />
            </uni-forms-item>
          </uni-forms>
        </view>
      </uni-popup-dialog>
    </uni-popup>

    <!-- 批量发送弹窗 -->
    <uni-popup ref="batchSendPopup" v-model="showBatchSendModal" type="dialog">
      <uni-popup-dialog 
        title="批量发送消息"
        @confirm="batchSendMessage"
        @close="showBatchSendModal = false"
      >
        <view class="batch-send-form">
          <uni-forms ref="batchSendForm" :model="batchSendForm" :rules="batchSendRules">
            <uni-forms-item label="消息标题" name="title" required>
              <uni-easyinput v-model="batchSendForm.title" placeholder="请输入消息标题" />
            </uni-forms-item>
            <uni-forms-item label="消息内容" name="content" required>
              <textarea 
                v-model="batchSendForm.content" 
                placeholder="请输入消息内容"
                class="message-textarea"
              />
            </uni-forms-item>
            <uni-forms-item label="发送对象" name="targetType" required>
              <uni-data-select
                v-model="batchSendForm.targetType"
                :localdata="targetOptions"
                placeholder="请选择发送对象"
              />
            </uni-forms-item>
          </uni-forms>
        </view>
      </uni-popup-dialog>
    </uni-popup>

    <!-- 消息详情弹窗 -->
    <uni-popup ref="messageDetailPopup" v-model="showMessageDetail" type="dialog">
      <uni-popup-dialog 
        title="消息详情"
        @close="showMessageDetail = false"
      >
        <view class="message-detail" v-if="currentMessage">
          <uni-forms :model="currentMessage" label-width="100px">
            <uni-forms-item label="消息类型">
              <uni-tag 
                :text="getTypeText(currentMessage.type)"
                :type="getTypeColor(currentMessage.type)"
              />
            </uni-forms-item>
            <uni-forms-item label="标题">{{ currentMessage.title }}</uni-forms-item>
            <uni-forms-item label="内容">
              <view class="full-content">{{ currentMessage.content }}</view>
            </uni-forms-item>
            <uni-forms-item label="发送人">{{ currentMessage.sender?.nickname || '系统' }}</uni-forms-item>
            <uni-forms-item label="接收人">{{ currentMessage.receiver?.nickname || '全体用户' }}</uni-forms-item>
            <uni-forms-item label="状态">
              <uni-tag 
                :text="getStatusText(currentMessage.status)"
                :type="getStatusType(currentMessage.status)"
              />
            </uni-forms-item>
            <uni-forms-item label="发送时间">{{ formatDate(currentMessage.send_date) }}</uni-forms-item>
            <uni-forms-item label="阅读时间" v-if="currentMessage.read_date">
              {{ formatDate(currentMessage.read_date) }}
            </uni-forms-item>
          </uni-forms>
        </view>
      </uni-popup-dialog>
    </uni-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      loading: false,
      messageList: [],
      showSendModal: false,
      showBatchSendModal: false,
      showMessageDetail: false,
      currentMessage: null,
      
      // 搜索表单
      searchForm: {
        keyword: '',
        type: null,
        status: null
      },
      
      // 发送消息表单
      sendForm: {
        title: '',
        content: '',
        receiverType: null
      },
      
      // 批量发送表单
      batchSendForm: {
        title: '',
        content: '',
        targetType: null
      },
      
      // 分页
      pagination: {
        page: 1,
        pageSize: 20,
        total: 0
      },
      
      // 选项数据
      typeOptions: [
        { value: null, text: '全部类型' },
        { value: 'system', text: '系统消息' },
        { value: 'notice', text: '通知消息' },
        { value: 'private', text: '私信' }
      ],
      
      statusOptions: [
        { value: null, text: '全部状态' },
        { value: 'unread', text: '未读' },
        { value: 'read', text: '已读' }
      ],
      
      receiverOptions: [
        { value: 'all', text: '全体用户' },
        { value: 'students', text: '学生用户' },
        { value: 'landlords', text: '房东用户' }
      ],
      
      targetOptions: [
        { value: 'all', text: '全体用户' },
        { value: 'students', text: '学生用户' },
        { value: 'landlords', text: '房东用户' },
        { value: 'active', text: '活跃用户' }
      ],
      
      // 表单验证规则
      sendRules: {
        title: {
          rules: [{ required: true, errorMessage: '请输入消息标题' }]
        },
        content: {
          rules: [{ required: true, errorMessage: '请输入消息内容' }]
        },
        receiverType: {
          rules: [{ required: true, errorMessage: '请选择接收人类型' }]
        }
      },
      
      batchSendRules: {
        title: {
          rules: [{ required: true, errorMessage: '请输入消息标题' }]
        },
        content: {
          rules: [{ required: true, errorMessage: '请输入消息内容' }]
        },
        targetType: {
          rules: [{ required: true, errorMessage: '请选择发送对象' }]
        }
      }
    }
  },
  
  onLoad() {
    this.loadMessageList();
  },
  
  methods: {
    // 加载消息列表
    async loadMessageList() {
      this.loading = true;
      try {
        // 模拟数据，实际项目中应该调用云函数
        const mockData = {
          list: [
            {
              _id: '1',
              type: 'system',
              title: '系统维护通知',
              content: '系统将于今晚22:00-24:00进行维护，期间可能无法正常使用，请提前做好准备。',
              sender: null,
              receiver: null,
              status: 'read',
              send_date: new Date('2024-01-10 10:00:00')
            },
            {
              _id: '2',
              type: 'notice',
              title: '新功能上线',
              content: '房源收藏功能已上线，快去体验吧！',
              sender: { nickname: '管理员' },
              receiver: null,
              status: 'unread',
              send_date: new Date('2024-01-11 14:30:00')
            }
          ],
          total: 2
        };

        this.messageList = mockData.list;
        this.pagination.total = mockData.total;

      } catch (error) {
        console.error('加载消息列表失败:', error);
        uni.showToast({
          title: '加载失败',
          icon: 'error'
        });
      } finally {
        this.loading = false;
      }
    },

    // 搜索
    handleSearch() {
      this.pagination.page = 1;
      this.loadMessageList();
    },

    // 重置搜索
    resetSearch() {
      this.searchForm = {
        keyword: '',
        type: null,
        status: null
      };
      this.handleSearch();
    },

    // 分页变化
    handlePageChange(e) {
      this.pagination.page = e.current;
      this.loadMessageList();
    },

    // 查看消息详情
    viewMessage(message) {
      this.currentMessage = message;
      this.showMessageDetail = true;
    },

    // 发送消息
    async sendMessage() {
      try {
        await this.$refs.sendForm.validate();

        // 这里应该调用云函数发送消息
        uni.showToast({
          title: '消息发送成功',
          icon: 'success'
        });

        this.showSendModal = false;
        this.sendForm = {
          title: '',
          content: '',
          receiverType: null
        };
        this.loadMessageList();
      } catch (error) {
        console.error('发送消息失败:', error);
        uni.showToast({
          title: '发送失败',
          icon: 'error'
        });
      }
    },

    // 批量发送消息
    async batchSendMessage() {
      try {
        await this.$refs.batchSendForm.validate();

        // 这里应该调用云函数批量发送消息
        uni.showToast({
          title: '批量发送成功',
          icon: 'success'
        });

        this.showBatchSendModal = false;
        this.batchSendForm = {
          title: '',
          content: '',
          targetType: null
        };
        this.loadMessageList();
      } catch (error) {
        console.error('批量发送消息失败:', error);
        uni.showToast({
          title: '发送失败',
          icon: 'error'
        });
      }
    },

    // 删除消息
    async deleteMessage(message) {
      const confirmResult = await uni.showModal({
        title: '确认删除',
        content: `确定要删除消息"${message.title}"吗？`
      });

      if (!confirmResult.confirm) return;

      try {
        // 这里应该调用云函数删除消息
        uni.showToast({
          title: '删除成功',
          icon: 'success'
        });
        this.loadMessageList();
      } catch (error) {
        console.error('删除消息失败:', error);
        uni.showToast({
          title: '删除失败',
          icon: 'error'
        });
      }
    },

    // 辅助方法
    getTypeText(type) {
      const typeMap = {
        system: '系统消息',
        notice: '通知消息',
        private: '私信'
      };
      return typeMap[type] || type;
    },

    getTypeColor(type) {
      const colorMap = {
        system: 'error',
        notice: 'primary',
        private: 'success'
      };
      return colorMap[type] || 'default';
    },

    getStatusText(status) {
      const statusMap = {
        unread: '未读',
        read: '已读'
      };
      return statusMap[status] || status;
    },

    getStatusType(status) {
      const typeMap = {
        unread: 'warning',
        read: 'success'
      };
      return typeMap[status] || 'default';
    },

    formatDate(date) {
      if (!date) return '-';
      return new Date(date).toLocaleString('zh-CN');
    }
  }
}
</script>

<style scoped>
.admin-message-management {
  padding: 20px;
}

.action-section {
  margin-bottom: 20px;
  display: flex;
  gap: 10px;
}

.search-section {
  margin-bottom: 20px;
}

.message-content {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: wrap;
}

.pagination-section {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.send-form, .batch-send-form {
  padding: 20px;
}

.message-textarea {
  width: 100%;
  min-height: 100px;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  resize: vertical;
}

.message-detail {
  max-height: 500px;
  overflow-y: auto;
}

.full-content {
  max-height: 200px;
  overflow-y: auto;
  line-height: 1.5;
  white-space: pre-wrap;
}
</style>
