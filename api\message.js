// 消息相关API
import { callAPI } from './index.js'

/**
 * 获取消息列表
 * @param {Object} params 查询参数
 * @param {number} params.page 页码
 * @param {number} params.pageSize 每页数量
 * @param {string} params.type 消息类型
 * @param {boolean} params.isRead 是否已读
 */
export function getMessageList(params = {}) {
  return callAPI('message-management', 'getMessageList', params)
}

/**
 * 标记消息为已读
 * @param {string} messageId 消息ID
 */
export function markMessageAsRead(messageId) {
  return callAPI('message-management', 'markAsRead', { messageId })
}

/**
 * 批量标记消息为已读
 * @param {Array} messageIds 消息ID数组
 */
export function batchMarkAsRead(messageIds) {
  return callAPI('message-management', 'batchMarkAsRead', { messageIds })
}

/**
 * 标记所有消息为已读
 */
export function markAllAsRead() {
  return callAPI('message-management', 'markAllAsRead', {})
}

/**
 * 删除消息
 * @param {string} messageId 消息ID
 */
export function deleteMessage(messageId) {
  return callAPI('message-management', 'deleteMessage', { messageId })
}

/**
 * 批量删除消息
 * @param {Array} messageIds 消息ID数组
 */
export function batchDeleteMessages(messageIds) {
  return callAPI('message-management', 'batchDeleteMessages', { messageIds })
}

/**
 * 获取未读消息数量
 */
export function getUnreadCount() {
  return callAPI('message-management', 'getUnreadCount', {})
}

/**
 * 获取消息统计
 */
export function getMessageStats() {
  return callAPI('message-management', 'getMessageStats', {})
}

/**
 * 发送系统消息（管理员功能）
 * @param {Object} messageData 消息数据
 * @param {string} messageData.userId 接收用户ID
 * @param {string} messageData.title 消息标题
 * @param {string} messageData.content 消息内容
 * @param {string} messageData.type 消息类型
 * @param {string} messageData.relatedId 关联ID
 */
export function sendSystemMessage(messageData) {
  return callAPI('message-management', 'sendSystemMessage', messageData)
}

/**
 * 清空所有消息
 */
export function clearAllMessages() {
  return callAPI('message-management', 'clearAllMessages', {})
}
