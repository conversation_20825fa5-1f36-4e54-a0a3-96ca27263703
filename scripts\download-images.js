// 图片下载脚本
// 注意：这个脚本需要在Node.js环境中运行，不是在小程序中运行

const https = require('https');
const fs = require('fs');
const path = require('path');

// 图片下载配置
const IMAGE_DOWNLOADS = {
  // Unsplash 免费图片 API
  // 注意：实际使用时需要替换为真实的图片URL
  
  // Logo和图标 (建议从 Iconfont 下载)
  'logo.png': 'https://images.unsplash.com/photo-1560518883-ce09059eeffa?w=120&h=120&fit=crop',
  
  // 默认头像
  'default-avatar.png': 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=200&h=200&fit=crop&crop=face',
  
  // 默认房源图片
  'default-house.png': 'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=400&h=300&fit=crop',
  
  // 空状态图片 (建议使用插画风格)
  'empty-default.png': 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=200&fit=crop',
  'empty-house.png': 'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=200&h=200&fit=crop',
  'empty-favorite.png': 'https://images.unsplash.com/photo-1493612276216-ee3925520721?w=200&h=200&fit=crop',
  'empty-search.png': 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=200&h=200&fit=crop',
  
  // 轮播图
  'banner1.jpg': 'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=750&h=300&fit=crop',
  'banner2.jpg': 'https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?w=750&h=300&fit=crop',
  'banner3.jpg': 'https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?w=750&h=300&fit=crop',
  
  // 地图标记 (建议从图标库下载)
  'map-marker.png': 'https://images.unsplash.com/photo-1569336415962-a4bd9f69cd83?w=30&h=30&fit=crop',
  
  // 测试房源图片
  'test-house.jpg': 'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=400&h=300&fit=crop'
};

// 创建 static 目录
const staticDir = path.join(__dirname, '../static');
if (!fs.existsSync(staticDir)) {
  fs.mkdirSync(staticDir, { recursive: true });
}

// 下载单个图片
function downloadImage(url, filename) {
  return new Promise((resolve, reject) => {
    const filePath = path.join(staticDir, filename);
    const file = fs.createWriteStream(filePath);
    
    https.get(url, (response) => {
      if (response.statusCode === 200) {
        response.pipe(file);
        file.on('finish', () => {
          file.close();
          console.log(`✅ 下载成功: ${filename}`);
          resolve(filePath);
        });
      } else {
        reject(new Error(`下载失败: ${response.statusCode}`));
      }
    }).on('error', (error) => {
      fs.unlink(filePath, () => {}); // 删除失败的文件
      reject(error);
    });
  });
}

// 批量下载图片
async function downloadAllImages() {
  console.log('🚀 开始下载图片资源...');
  console.log(`📁 目标目录: ${staticDir}`);
  
  const downloadPromises = Object.entries(IMAGE_DOWNLOADS).map(([filename, url]) => {
    return downloadImage(url, filename).catch(error => {
      console.error(`❌ 下载失败 ${filename}:`, error.message);
      return null;
    });
  });
  
  try {
    const results = await Promise.all(downloadPromises);
    const successCount = results.filter(result => result !== null).length;
    const totalCount = Object.keys(IMAGE_DOWNLOADS).length;
    
    console.log(`\n📊 下载完成: ${successCount}/${totalCount} 个文件`);
    
    if (successCount < totalCount) {
      console.log('\n⚠️  部分图片下载失败，请手动下载以下图片：');
      Object.entries(IMAGE_DOWNLOADS).forEach(([filename, url], index) => {
        if (results[index] === null) {
          console.log(`   ${filename}: ${url}`);
        }
      });
    }
    
    console.log('\n✨ 图片资源配置完成！');
    
  } catch (error) {
    console.error('❌ 批量下载失败:', error);
  }
}

// 检查图片是否存在
function checkImages() {
  console.log('🔍 检查图片资源...');
  
  const missingImages = [];
  
  Object.keys(IMAGE_DOWNLOADS).forEach(filename => {
    const filePath = path.join(staticDir, filename);
    if (!fs.existsSync(filePath)) {
      missingImages.push(filename);
    }
  });
  
  if (missingImages.length === 0) {
    console.log('✅ 所有图片资源都已存在');
  } else {
    console.log(`❌ 缺少 ${missingImages.length} 个图片文件:`);
    missingImages.forEach(filename => {
      console.log(`   - ${filename}`);
    });
  }
  
  return missingImages;
}

// 生成图片清单
function generateImageManifest() {
  const manifestPath = path.join(staticDir, 'image-manifest.json');
  const manifest = {
    generated: new Date().toISOString(),
    images: {}
  };
  
  Object.keys(IMAGE_DOWNLOADS).forEach(filename => {
    const filePath = path.join(staticDir, filename);
    if (fs.existsSync(filePath)) {
      const stats = fs.statSync(filePath);
      manifest.images[filename] = {
        size: stats.size,
        modified: stats.mtime.toISOString(),
        path: `/static/${filename}`
      };
    }
  });
  
  fs.writeFileSync(manifestPath, JSON.stringify(manifest, null, 2));
  console.log(`📋 图片清单已生成: ${manifestPath}`);
}

// 主函数
async function main() {
  const command = process.argv[2];
  
  switch (command) {
    case 'download':
      await downloadAllImages();
      generateImageManifest();
      break;
      
    case 'check':
      checkImages();
      break;
      
    case 'manifest':
      generateImageManifest();
      break;
      
    default:
      console.log(`
📖 图片资源管理脚本

用法:
  node scripts/download-images.js <command>

命令:
  download  - 下载所有图片资源
  check     - 检查图片资源是否完整
  manifest  - 生成图片清单文件

示例:
  node scripts/download-images.js download
  node scripts/download-images.js check
      `);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  downloadImage,
  downloadAllImages,
  checkImages,
  generateImageManifest,
  IMAGE_DOWNLOADS
};
