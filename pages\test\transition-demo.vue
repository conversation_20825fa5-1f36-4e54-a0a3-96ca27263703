<template>
  <view class="demo-container">
    <view class="demo-header">
      <text class="demo-title">过渡效果优化演示</text>
      <text class="demo-subtitle">查看头部与内容的自然过渡</text>
    </view>
    
    <view class="demo-content">
      <!-- 优化说明 -->
      <view class="optimization-section">
        <view class="section-title">🎨 优化内容</view>
        
        <view class="optimization-item">
          <view class="item-icon">🔄</view>
          <view class="item-content">
            <text class="item-title">圆角过渡</text>
            <text class="item-desc">头部添加底部圆角，创造自然的视觉过渡</text>
          </view>
        </view>
        
        <view class="optimization-item">
          <view class="item-icon">📐</view>
          <view class="item-content">
            <text class="item-title">间距调整</text>
            <text class="item-desc">增加头部底部间距，轮播图负边距重叠</text>
          </view>
        </view>
        
        <view class="optimization-item">
          <view class="item-icon">💎</view>
          <view class="item-content">
            <text class="item-title">阴影优化</text>
            <text class="item-desc">轮播图多层阴影，增强立体感和层次</text>
          </view>
        </view>
        
        <view class="optimization-item">
          <view class="item-icon">🎯</view>
          <view class="item-content">
            <text class="item-title">背景简化</text>
            <text class="item-desc">移除复杂渐变，使用纯色背景更清爽</text>
          </view>
        </view>
      </view>
      
      <!-- 对比展示 */
      <view class="comparison-section">
        <view class="section-title">📊 效果对比</view>
        
        <view class="comparison-grid">
          <view class="comparison-card before">
            <view class="card-header">优化前</view>
            <view class="card-content">
              <text class="card-point">• 直角边缘，过渡生硬</text>
              <text class="card-point">• 渐变背景与内容冲突</text>
              <text class="card-point">• 轮播图与头部分离感强</text>
              <text class="card-point">• 视觉层次不够清晰</text>
            </view>
          </view>
          
          <view class="comparison-card after">
            <view class="card-header">优化后</view>
            <view class="card-content">
              <text class="card-point">• 圆角设计，过渡自然</text>
              <text class="card-point">• 纯色背景，视觉清爽</text>
              <text class="card-point">• 轮播图与头部融合</text>
              <text class="card-point">• 层次分明，重点突出</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 技术细节 -->
      <view class="technical-section">
        <view class="section-title">⚙️ 技术实现</view>
        
        <view class="tech-list">
          <view class="tech-item">
            <text class="tech-label">圆角设计</text>
            <text class="tech-value">border-radius: 0 0 40rpx 40rpx</text>
          </view>
          
          <view class="tech-item">
            <text class="tech-label">负边距重叠</text>
            <text class="tech-value">margin-top: -80rpx</text>
          </view>
          
          <view class="tech-item">
            <text class="tech-label">多层阴影</text>
            <text class="tech-value">box-shadow: 0 16rpx 48rpx, 0 4rpx 16rpx</text>
          </view>
          
          <view class="tech-item">
            <text class="tech-label">层级控制</text>
            <text class="tech-value">z-index: 2</text>
          </view>
        </view>
      </view>
      
      <!-- 测试按钮 -->
      <view class="test-section">
        <view class="section-title">🧪 体验测试</view>
        
        <button class="test-btn primary" @click="goToIndex">
          查看优化后的首页
        </button>
        
        <button class="test-btn" @click="testScroll">
          测试滚动效果
        </button>
        
        <button class="test-btn" @click="goBack">
          返回
        </button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  methods: {
    goToIndex() {
      uni.switchTab({
        url: '/pages/index/index'
      });
    },
    
    testScroll() {
      uni.showToast({
        title: '请在首页体验滚动效果',
        icon: 'none'
      });
      setTimeout(() => {
        this.goToIndex();
      }, 1500);
    },
    
    goBack() {
      uni.navigateBack();
    }
  }
}
</script>

<style scoped>
.demo-container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  padding: 40rpx;
}

.demo-header {
  text-align: center;
  margin-bottom: 60rpx;
  padding-top: 60rpx;
}

.demo-title {
  display: block;
  font-size: 40rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 20rpx;
}

.demo-subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.demo-content {
  background: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  gap: 50rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #303133;
  margin-bottom: 30rpx;
}

.optimization-item {
  display: flex;
  align-items: flex-start;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
}

.item-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
  margin-top: 4rpx;
}

.item-content {
  flex: 1;
}

.item-title {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8rpx;
}

.item-desc {
  display: block;
  font-size: 24rpx;
  color: #909399;
  line-height: 1.5;
}

.comparison-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.comparison-card {
  border-radius: 12rpx;
  padding: 24rpx;
  border: 2rpx solid;
}

.comparison-card.before {
  background: #fef2f2;
  border-color: #fecaca;
}

.comparison-card.after {
  background: #f0fdf4;
  border-color: #bbf7d0;
}

.card-header {
  font-size: 26rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
  text-align: center;
}

.comparison-card.before .card-header {
  color: #dc2626;
}

.comparison-card.after .card-header {
  color: #16a34a;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.card-point {
  font-size: 22rpx;
  color: #4b5563;
  line-height: 1.4;
}

.tech-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.tech-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border-left: 4rpx solid #007aff;
}

.tech-label {
  font-size: 26rpx;
  font-weight: 600;
  color: #303133;
}

.tech-value {
  font-size: 22rpx;
  color: #007aff;
  font-family: 'Monaco', 'Menlo', monospace;
}

.test-btn {
  width: 100%;
  height: 88rpx;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #f5f7fa;
  color: #606266;
  margin-bottom: 16rpx;
}

.test-btn.primary {
  background: #007aff;
  color: #fff;
}

.test-btn:active {
  opacity: 0.8;
}

@media (max-width: 750rpx) {
  .comparison-grid {
    grid-template-columns: 1fr;
  }
  
  .tech-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8rpx;
  }
}
</style>
