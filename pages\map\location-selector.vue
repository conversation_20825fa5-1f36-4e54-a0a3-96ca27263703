<template>
  <view class="location-selector">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-content">
        <view class="nav-left" @click="goBack">
          <uni-icons type="arrowleft" size="20" color="#333"></uni-icons>
          <text class="nav-text">返回</text>
        </view>
        <text class="nav-title">选择位置</text>
        <view class="nav-right" @click="confirmLocation" :class="{ disabled: !selectedLocation }">
          <text class="confirm-text">确定</text>
        </view>
      </view>
    </view>

    <!-- 搜索栏 -->
    <view class="search-container">
      <view class="search-bar">
        <uni-icons type="search" size="18" color="#999"></uni-icons>
        <input 
          class="search-input" 
          type="text" 
          placeholder="搜索地点、小区、地标" 
          v-model="searchKeyword"
          @input="onSearchInput"
          @confirm="performSearch"
        />
        <view v-if="searchKeyword" class="clear-btn" @click="clearSearch">
          <uni-icons type="clear" size="16" color="#999"></uni-icons>
        </view>
      </view>
    </view>

    <!-- 地图区域 -->
    <view class="map-container">
      <map
        id="locationMap"
        class="location-map"
        :longitude="mapCenter.longitude"
        :latitude="mapCenter.latitude"
        :scale="mapScale"
        :markers="markers"
        :show-location="true"
        @tap="onMapTap"
        @regionchange="onRegionChange"
        @markertap="onMarkerTap"
      >
        <!-- 中心点标记 -->
        <cover-view class="center-marker">
          <cover-image src="/static/map-pin.png" class="marker-image"></cover-image>
        </cover-view>
        
        <!-- 定位按钮 -->
        <cover-view class="location-control" @tap="getCurrentLocation">
          <cover-image src="/static/location-btn.png" class="control-image"></cover-image>
        </cover-view>
      </map>
    </view>

    <!-- 位置信息面板 -->
    <view class="info-panel">
      <!-- 当前选中位置 -->
      <view class="current-location" v-if="selectedLocation">
        <view class="location-header">
          <text class="location-title">{{ selectedLocation.name || '选中位置' }}</text>
          <view class="location-status" :class="{ loading: isGettingAddress }">
            <text class="status-text">{{ isGettingAddress ? '获取地址中...' : '已选中' }}</text>
          </view>
        </view>
        <text class="location-address">{{ selectedLocation.address || '正在获取详细地址...' }}</text>
        <view class="location-coords">
          <text class="coords-text">
            经度: {{ selectedLocation.longitude?.toFixed(6) }} | 
            纬度: {{ selectedLocation.latitude?.toFixed(6) }}
          </text>
        </view>
      </view>

      <!-- 搜索结果列表 -->
      <scroll-view 
        v-if="searchResults.length > 0" 
        class="search-results" 
        scroll-y
        :style="{ maxHeight: '300rpx' }"
      >
        <view class="result-header">
          <text class="result-title">搜索结果</text>
          <text class="result-count">{{ searchResults.length }}个结果</text>
        </view>
        <view 
          class="search-item" 
          v-for="(item, index) in searchResults" 
          :key="index"
          @click="selectSearchResult(item)"
        >
          <view class="item-icon">
            <uni-icons type="location" size="16" color="#007aff"></uni-icons>
          </view>
          <view class="item-content">
            <text class="item-name">{{ item.name }}</text>
            <text class="item-address">{{ item.address }}</text>
          </view>
          <view class="item-distance" v-if="item.distance">
            <text class="distance-text">{{ formatDistance(item.distance) }}</text>
          </view>
        </view>
      </scroll-view>

      <!-- 附近地点 -->
      <view v-else-if="nearbyPlaces.length > 0" class="nearby-places">
        <view class="nearby-header">
          <text class="nearby-title">附近地点</text>
          <text class="refresh-btn" @click="refreshNearby">刷新</text>
        </view>
        <scroll-view class="nearby-list" scroll-y :style="{ maxHeight: '250rpx' }">
          <view 
            class="nearby-item" 
            v-for="(place, index) in nearbyPlaces" 
            :key="index"
            @click="selectNearbyPlace(place)"
          >
            <view class="place-icon">
              <text class="icon-emoji">{{ getPlaceIcon(place.category) }}</text>
            </view>
            <view class="place-content">
              <text class="place-name">{{ place.name }}</text>
              <text class="place-category">{{ place.category }}</text>
            </view>
            <view class="place-distance">
              <text class="distance-text">{{ formatDistance(place.distance) }}</text>
            </view>
          </view>
        </scroll-view>
      </view>

      <!-- 空状态 -->
      <view v-else class="empty-state">
        <text class="empty-text">点击地图选择位置，或搜索地点名称</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      statusBarHeight: 44,
      mapCenter: {
        longitude: 116.397470, // 默认北京天安门
        latitude: 39.908823
      },
      mapScale: 16,
      markers: [],
      selectedLocation: null,
      isGettingAddress: false,
      searchKeyword: '',
      searchResults: [],
      nearbyPlaces: [],
      searchTimer: null,
      mapContext: null
    }
  },
  
  onLoad(options) {
    // 获取系统状态栏高度
    const systemInfo = uni.getSystemInfoSync()
    this.statusBarHeight = systemInfo.statusBarHeight || 44
    
    // 创建地图上下文
    this.mapContext = uni.createMapContext('locationMap', this)
    
    // 如果有传入的位置信息，则显示
    if (options.latitude && options.longitude) {
      this.mapCenter = {
        latitude: parseFloat(options.latitude),
        longitude: parseFloat(options.longitude)
      }
      this.selectedLocation = {
        latitude: parseFloat(options.latitude),
        longitude: parseFloat(options.longitude),
        address: options.address || '',
        name: options.name || '当前位置'
      }
      this.updateMarkers()
    } else {
      // 获取当前位置
      this.getCurrentLocation()
    }
  },
  
  methods: {
    // 获取当前位置
    getCurrentLocation() {
      uni.showLoading({ title: '定位中...' })
      
      uni.getLocation({
        type: 'gcj02',
        success: (res) => {
          console.log('获取位置成功:', res)
          this.mapCenter = {
            latitude: res.latitude,
            longitude: res.longitude
          }
          this.selectedLocation = {
            latitude: res.latitude,
            longitude: res.longitude,
            name: '当前位置',
            address: '正在获取地址...'
          }
          this.updateMarkers()
          this.getAddressFromCoords(res.latitude, res.longitude)
          this.getNearbyPlaces(res.latitude, res.longitude)
        },
        fail: (error) => {
          console.error('获取位置失败:', error)
          uni.showToast({
            title: '定位失败，请手动选择',
            icon: 'none'
          })
          // 使用默认位置
          this.selectedLocation = {
            latitude: this.mapCenter.latitude,
            longitude: this.mapCenter.longitude,
            name: '默认位置',
            address: '北京市天安门广场'
          }
          this.updateMarkers()
        },
        complete: () => {
          uni.hideLoading()
        }
      })
    },
    
    // 地图点击事件
    onMapTap(e) {
      const { latitude, longitude } = e.detail
      console.log('地图点击:', latitude, longitude)
      
      this.selectedLocation = {
        latitude,
        longitude,
        name: '选中位置',
        address: '正在获取地址...'
      }
      
      this.mapCenter = { latitude, longitude }
      this.updateMarkers()
      this.getAddressFromCoords(latitude, longitude)
    },
    
    // 地图区域变化
    onRegionChange(e) {
      if (e.type === 'end') {
        // 获取地图中心点
        this.mapContext.getCenterLocation({
          success: (res) => {
            this.selectedLocation = {
              latitude: res.latitude,
              longitude: res.longitude,
              name: '地图中心',
              address: '正在获取地址...'
            }
            this.getAddressFromCoords(res.latitude, res.longitude)
          }
        })
      }
    },
    
    // 更新地图标记
    updateMarkers() {
      if (this.selectedLocation) {
        this.markers = [{
          id: 1,
          latitude: this.selectedLocation.latitude,
          longitude: this.selectedLocation.longitude,
          iconPath: '/static/map-marker.png',
          width: 30,
          height: 30,
          callout: {
            content: this.selectedLocation.name || '选中位置',
            color: '#333',
            fontSize: 12,
            borderRadius: 4,
            bgColor: '#fff',
            padding: 8,
            display: 'ALWAYS'
          }
        }]
      }
    },
    
    // 根据坐标获取地址
    getAddressFromCoords(latitude, longitude) {
      this.isGettingAddress = true
      
      // 这里应该调用真实的地理编码API
      // 暂时使用模拟数据
      setTimeout(() => {
        const mockAddress = this.generateMockAddress(latitude, longitude)
        if (this.selectedLocation) {
          this.selectedLocation.address = mockAddress
          this.selectedLocation.name = this.extractLocationName(mockAddress)
        }
        this.isGettingAddress = false
        this.updateMarkers()
      }, 1000)
    },
    
    // 生成模拟地址
    generateMockAddress(lat, lng) {
      const districts = ['朝阳区', '海淀区', '西城区', '东城区', '丰台区', '石景山区']
      const streets = ['中关村大街', '王府井大街', '长安街', '建国门外大街', '复兴路']
      const district = districts[Math.floor(Math.random() * districts.length)]
      const street = streets[Math.floor(Math.random() * streets.length)]
      const number = Math.floor(Math.random() * 999) + 1
      
      return `北京市${district}${street}${number}号`
    },
    
    // 提取位置名称
    extractLocationName(address) {
      // 简单提取街道名称作为位置名称
      const match = address.match(/(.+?街|.+?路|.+?大街|.+?大道)/)
      return match ? match[1] + '附近' : '选中位置'
    },
    
    // 搜索输入
    onSearchInput() {
      if (this.searchTimer) {
        clearTimeout(this.searchTimer)
      }
      
      this.searchTimer = setTimeout(() => {
        if (this.searchKeyword.trim()) {
          this.performSearch()
        } else {
          this.searchResults = []
        }
      }, 500)
    },
    
    // 执行搜索
    performSearch() {
      if (!this.searchKeyword.trim()) return
      
      uni.showLoading({ title: '搜索中...' })
      
      // 模拟搜索结果
      setTimeout(() => {
        this.searchResults = this.generateMockSearchResults(this.searchKeyword)
        uni.hideLoading()
      }, 800)
    },
    
    // 生成模拟搜索结果
    generateMockSearchResults(keyword) {
      const results = []
      for (let i = 1; i <= 5; i++) {
        results.push({
          name: `${keyword}相关地点${i}`,
          address: `北京市朝阳区${keyword}街道${i}号`,
          latitude: this.mapCenter.latitude + (Math.random() - 0.5) * 0.01,
          longitude: this.mapCenter.longitude + (Math.random() - 0.5) * 0.01,
          distance: Math.floor(Math.random() * 2000) + 100
        })
      }
      return results
    },
    
    // 选择搜索结果
    selectSearchResult(item) {
      this.selectedLocation = {
        ...item,
        name: item.name
      }
      this.mapCenter = {
        latitude: item.latitude,
        longitude: item.longitude
      }
      this.updateMarkers()
      this.searchResults = []
      this.searchKeyword = ''
    },
    
    // 获取附近地点
    getNearbyPlaces(latitude, longitude) {
      // 模拟附近地点数据
      this.nearbyPlaces = [
        { name: '万达广场', category: '购物中心', distance: 150 },
        { name: '地铁站', category: '交通设施', distance: 200 },
        { name: '人民医院', category: '医疗机构', distance: 350 },
        { name: '实验小学', category: '教育机构', distance: 400 },
        { name: '中央公园', category: '休闲娱乐', distance: 600 }
      ].map(place => ({
        ...place,
        latitude: latitude + (Math.random() - 0.5) * 0.002,
        longitude: longitude + (Math.random() - 0.5) * 0.002
      }))
    },
    
    // 选择附近地点
    selectNearbyPlace(place) {
      this.selectedLocation = {
        latitude: place.latitude,
        longitude: place.longitude,
        name: place.name,
        address: `${place.name}附近`
      }
      this.mapCenter = {
        latitude: place.latitude,
        longitude: place.longitude
      }
      this.updateMarkers()
      this.getAddressFromCoords(place.latitude, place.longitude)
    },
    
    // 获取地点图标
    getPlaceIcon(category) {
      const icons = {
        '购物中心': '🛒',
        '交通设施': '🚇',
        '医疗机构': '🏥',
        '教育机构': '🏫',
        '休闲娱乐': '🌳',
        '餐饮美食': '🍽️',
        '住宅小区': '🏠'
      }
      return icons[category] || '📍'
    },
    
    // 格式化距离
    formatDistance(distance) {
      if (distance < 1000) {
        return `${distance}m`
      } else {
        return `${(distance / 1000).toFixed(1)}km`
      }
    },
    
    // 清除搜索
    clearSearch() {
      this.searchKeyword = ''
      this.searchResults = []
    },
    
    // 刷新附近地点
    refreshNearby() {
      if (this.selectedLocation) {
        this.getNearbyPlaces(this.selectedLocation.latitude, this.selectedLocation.longitude)
      }
    },
    
    // 确认位置选择
    confirmLocation() {
      if (!this.selectedLocation) {
        uni.showToast({
          title: '请先选择位置',
          icon: 'none'
        })
        return
      }
      
      // 返回选中的位置信息给上一页
      const pages = getCurrentPages()
      const prevPage = pages[pages.length - 2]
      
      if (prevPage && prevPage.onLocationSelected) {
        prevPage.onLocationSelected(this.selectedLocation)
      }
      
      uni.navigateBack()
    },
    
    // 返回上一页
    goBack() {
      uni.navigateBack()
    }
  }
}
</script>

<style scoped>
.location-selector {
  height: 100vh;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
}

/* 自定义导航栏 */
.custom-navbar {
  background: #fff;
  border-bottom: 1rpx solid #e5e5e5;
  position: relative;
  z-index: 999;
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 30rpx;
}

.nav-left {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.nav-text {
  font-size: 28rpx;
  color: #333;
}

.nav-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.nav-right {
  padding: 12rpx 24rpx;
  background: #007aff;
  border-radius: 20rpx;
  transition: all 0.3s ease;
}

.nav-right.disabled {
  background: #ccc;
  opacity: 0.6;
}

.confirm-text {
  font-size: 26rpx;
  color: #fff;
  font-weight: 500;
}

/* 搜索容器 */
.search-container {
  background: #fff;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #e5e5e5;
}

.search-bar {
  background: #f5f5f5;
  border-radius: 25rpx;
  padding: 20rpx 30rpx;
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.clear-btn {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 地图容器 */
.map-container {
  flex: 1;
  position: relative;
  min-height: 400rpx;
}

.location-map {
  width: 100%;
  height: 100%;
}

.center-marker {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -100%);
  z-index: 10;
}

.marker-image {
  width: 60rpx;
  height: 60rpx;
}

.location-control {
  position: absolute;
  bottom: 40rpx;
  right: 40rpx;
  width: 80rpx;
  height: 80rpx;
  background: #fff;
  border-radius: 50%;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
}

.control-image {
  width: 40rpx;
  height: 40rpx;
}

/* 信息面板 */
.info-panel {
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  padding: 30rpx;
  max-height: 50vh;
  overflow: hidden;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
}

/* 当前位置信息 */
.current-location {
  margin-bottom: 30rpx;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  border-left: 6rpx solid #007aff;
}

.location-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12rpx;
}

.location-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

.location-status {
  padding: 6rpx 12rpx;
  background: #e8f5e8;
  border-radius: 12rpx;
}

.location-status.loading {
  background: #fff3cd;
}

.status-text {
  font-size: 22rpx;
  color: #28a745;
}

.location-status.loading .status-text {
  color: #856404;
}

.location-address {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
  margin-bottom: 12rpx;
}

.location-coords {
  padding-top: 12rpx;
  border-top: 1rpx solid #e0e0e0;
}

.coords-text {
  font-size: 22rpx;
  color: #999;
  font-family: 'Monaco', 'Menlo', monospace;
}

/* 搜索结果 */
.search-results {
  margin-bottom: 20rpx;
}

.result-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.result-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.result-count {
  font-size: 24rpx;
  color: #666;
}

.search-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border-radius: 12rpx;
  margin-bottom: 12rpx;
  background: #f8f9fa;
  transition: background 0.3s ease;
}

.search-item:active {
  background: #e3f2fd;
}

.item-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  border-radius: 50%;
  margin-right: 20rpx;
}

.item-content {
  flex: 1;
}

.item-name {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.item-address {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.3;
}

.item-distance {
  margin-left: 20rpx;
}

.distance-text {
  font-size: 22rpx;
  color: #999;
}

/* 附近地点 */
.nearby-places {
  margin-bottom: 20rpx;
}

.nearby-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.nearby-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.refresh-btn {
  font-size: 24rpx;
  color: #007aff;
  padding: 8rpx 16rpx;
  background: #f0f9ff;
  border-radius: 12rpx;
}

.nearby-list {
  max-height: 250rpx;
}

.nearby-item {
  display: flex;
  align-items: center;
  padding: 16rpx 20rpx;
  border-radius: 12rpx;
  margin-bottom: 8rpx;
  background: #f8f9fa;
  transition: background 0.3s ease;
}

.nearby-item:active {
  background: #e3f2fd;
}

.place-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  border-radius: 50%;
  margin-right: 20rpx;
}

.icon-emoji {
  font-size: 24rpx;
}

.place-content {
  flex: 1;
}

.place-name {
  display: block;
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 4rpx;
}

.place-category {
  display: block;
  font-size: 22rpx;
  color: #666;
}

.place-distance {
  margin-left: 20rpx;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60rpx 20rpx;
}

.empty-text {
  font-size: 26rpx;
  color: #999;
  line-height: 1.5;
}
</style>
