<template>
  <view class="edit-container">
    <form @submit="handleSubmit" v-if="houseInfo._id">
      <!-- 基本信息 -->
      <view class="form-section">
        <view class="section-title">基本信息</view>
        
        <view class="form-item">
          <text class="label">房源标题 *</text>
          <input 
            class="input" 
            type="text" 
            placeholder="请输入房源标题" 
            v-model="form.title"
            maxlength="50"
          />
        </view>
        
        <view class="form-item">
          <text class="label">房源描述</text>
          <textarea 
            class="textarea" 
            placeholder="请详细描述房源情况，如周边环境、交通等" 
            v-model="form.description"
            maxlength="500"
          ></textarea>
        </view>
        
        <view class="form-item">
          <text class="label">房源类型 *</text>
          <picker 
            mode="selector" 
            :range="houseTypes" 
            range-key="label"
            :value="typeIndex"
            @change="onTypeChange"
          >
            <view class="picker">
              {{ form.type ? getTypeLabel(form.type) : '请选择房源类型' }}
              <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
            </view>
          </picker>
        </view>
      </view>
      
      <!-- 房屋信息 -->
      <view class="form-section">
        <view class="section-title">房屋信息</view>
        
        <view class="form-row">
          <view class="form-item half">
            <text class="label">租金(元/月) *</text>
            <input 
              class="input" 
              type="number" 
              placeholder="租金" 
              v-model="form.price"
            />
          </view>
          <view class="form-item half">
            <text class="label">押金(元)</text>
            <input 
              class="input" 
              type="number" 
              placeholder="押金" 
              v-model="form.deposit"
            />
          </view>
        </view>
        
        <view class="form-row">
          <view class="form-item half">
            <text class="label">面积(㎡)</text>
            <input 
              class="input" 
              type="number" 
              placeholder="面积" 
              v-model="form.area"
            />
          </view>
          <view class="form-item half">
            <text class="label">朝向</text>
            <picker 
              mode="selector" 
              :range="orientations"
              :value="orientationIndex"
              @change="onOrientationChange"
            >
              <view class="picker">
                {{ form.orientation || '请选择' }}
                <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
              </view>
            </picker>
          </view>
        </view>
        
        <view class="form-row">
          <view class="form-item third">
            <text class="label">房间数</text>
            <input 
              class="input" 
              type="number" 
              placeholder="房间" 
              v-model="form.room_count"
            />
          </view>
          <view class="form-item third">
            <text class="label">客厅数</text>
            <input 
              class="input" 
              type="number" 
              placeholder="客厅" 
              v-model="form.hall_count"
            />
          </view>
          <view class="form-item third">
            <text class="label">卫生间</text>
            <input 
              class="input" 
              type="number" 
              placeholder="卫生间" 
              v-model="form.bathroom_count"
            />
          </view>
        </view>
        
        <view class="form-item">
          <text class="label">装修情况</text>
          <picker 
            mode="selector" 
            :range="decorationTypes"
            :value="decorationIndex"
            @change="onDecorationChange"
          >
            <view class="picker">
              {{ form.decoration || '请选择装修情况' }}
              <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
            </view>
          </picker>
        </view>
      </view>
      
      <!-- 房屋设施 -->
      <view class="form-section">
        <view class="section-title">房屋设施</view>
        <view class="facilities-grid">
          <view 
            class="facility-item" 
            v-for="facility in facilitiesOptions" 
            :key="facility.value"
            :class="{ active: form.facilities.includes(facility.value) }"
            @click="toggleFacility(facility.value)"
          >
            <text class="facility-text">{{ facility.label }}</text>
          </view>
        </view>
      </view>
      
      <!-- 联系方式 -->
      <view class="form-section">
        <view class="section-title">联系方式</view>
        
        <view class="form-item">
          <text class="label">联系人 *</text>
          <input 
            class="input" 
            type="text" 
            placeholder="请输入联系人姓名" 
            v-model="form.contact.name"
          />
        </view>
        
        <view class="form-item">
          <text class="label">联系电话 *</text>
          <input 
            class="input" 
            type="number" 
            placeholder="请输入联系电话" 
            v-model="form.contact.phone"
          />
        </view>
        
        <view class="form-item">
          <text class="label">微信号</text>
          <input 
            class="input" 
            type="text" 
            placeholder="请输入微信号" 
            v-model="form.contact.wechat"
          />
        </view>
      </view>
      
      <!-- 提交按钮 -->
      <view class="submit-section">
        <button class="submit-btn" @click="handleSubmit" :disabled="!canSubmit">
          {{ loading ? '保存中...' : '保存修改' }}
        </button>
      </view>
    </form>
    
    <!-- 加载状态 -->
    <view class="loading-container" v-else-if="loading">
      <text class="loading-text">加载中...</text>
    </view>
    
    <!-- 错误状态 -->
    <view class="error-container" v-else>
      <text class="error-text">房源不存在或无权限编辑</text>
      <button class="back-btn" @click="goBack">返回</button>
    </view>
  </view>
</template>

<script>
import request from '@/utils/request.js'
import { validatePhone } from '@/utils/common.js'
import { HOUSE_TYPES, ORIENTATIONS, DECORATION_TYPES, FACILITIES } from '@/common/config.js'

export default {
  data() {
    return {
      houseId: '',
      houseInfo: {},
      form: {
        title: '',
        description: '',
        type: '',
        price: '',
        deposit: '',
        area: '',
        room_count: '',
        hall_count: '',
        bathroom_count: '',
        floor: '',
        total_floors: '',
        orientation: '',
        decoration: '',
        facilities: [],
        contact: {
          name: '',
          phone: '',
          wechat: ''
        }
      },
      loading: false,
      
      // 选择器数据
      houseTypes: HOUSE_TYPES,
      orientations: ORIENTATIONS,
      decorationTypes: DECORATION_TYPES,
      facilitiesOptions: FACILITIES,
      
      // 选择器索引
      typeIndex: 0,
      orientationIndex: 0,
      decorationIndex: 0
    }
  },
  computed: {
    canSubmit() {
      const { title, type, price, contact } = this.form
      return title.trim() && 
             type && 
             price && 
             contact.name.trim() && 
             contact.phone.trim() && 
             !this.loading
    }
  },
  methods: {
    // 获取类型标签
    getTypeLabel(value) {
      const type = this.houseTypes.find(item => item.value === value)
      return type ? type.label : value
    },
    
    // 加载房源信息
    async loadHouseInfo() {
      this.loading = true
      
      try {
        const result = await request.callFunction('house-management', {
          action: 'getHouseDetail',
          data: {
            house_id: this.houseId
          }
        })
        
        if (result.code === 0) {
          this.houseInfo = result.data
          this.initForm()
        }
      } catch (error) {
        console.error('加载房源信息失败:', error)
      } finally {
        this.loading = false
      }
    },
    
    // 初始化表单
    initForm() {
      const house = this.houseInfo
      
      this.form = {
        title: house.title || '',
        description: house.description || '',
        type: house.type || '',
        price: house.price || '',
        deposit: house.deposit || '',
        area: house.area || '',
        room_count: house.room_count || '',
        hall_count: house.hall_count || '',
        bathroom_count: house.bathroom_count || '',
        floor: house.floor || '',
        total_floors: house.total_floors || '',
        orientation: house.orientation || '',
        decoration: house.decoration || '',
        facilities: house.facilities || [],
        contact: {
          name: house.contact?.name || '',
          phone: house.contact?.phone || '',
          wechat: house.contact?.wechat || ''
        }
      }
      
      // 设置选择器索引
      this.typeIndex = this.houseTypes.findIndex(item => item.value === house.type)
      this.orientationIndex = this.orientations.findIndex(item => item === house.orientation)
      this.decorationIndex = this.decorationTypes.findIndex(item => item === house.decoration)
    },
    
    // 房源类型选择
    onTypeChange(e) {
      const index = e.detail.value
      this.typeIndex = index
      this.form.type = this.houseTypes[index].value
    },
    
    // 朝向选择
    onOrientationChange(e) {
      const index = e.detail.value
      this.orientationIndex = index
      this.form.orientation = this.orientations[index]
    },
    
    // 装修情况选择
    onDecorationChange(e) {
      const index = e.detail.value
      this.decorationIndex = index
      this.form.decoration = this.decorationTypes[index]
    },
    
    // 切换设施
    toggleFacility(value) {
      const index = this.form.facilities.indexOf(value)
      if (index > -1) {
        this.form.facilities.splice(index, 1)
      } else {
        this.form.facilities.push(value)
      }
    },
    
    // 提交表单
    async handleSubmit() {
      if (!this.canSubmit) return
      
      // 表单验证
      if (!validatePhone(this.form.contact.phone)) {
        uni.showToast({
          title: '联系电话格式不正确',
          icon: 'none'
        })
        return
      }
      
      if (this.form.price <= 0) {
        uni.showToast({
          title: '租金必须大于0',
          icon: 'none'
        })
        return
      }
      
      this.loading = true
      
      try {
        const result = await request.callFunction('house-management', {
          action: 'updateHouse',
          data: {
            house_id: this.houseId,
            ...this.form,
            price: Number(this.form.price),
            deposit: Number(this.form.deposit) || 0,
            area: Number(this.form.area) || 0,
            room_count: Number(this.form.room_count) || 0,
            hall_count: Number(this.form.hall_count) || 0,
            bathroom_count: Number(this.form.bathroom_count) || 0,
            floor: Number(this.form.floor) || 0,
            total_floors: Number(this.form.total_floors) || 0
          }
        })
        
        if (result.code === 0) {
          uni.showToast({
            title: '保存成功',
            icon: 'success'
          })
          
          setTimeout(() => {
            uni.navigateBack()
          }, 1500)
        }
      } catch (error) {
        console.error('保存失败:', error)
      } finally {
        this.loading = false
      }
    },
    
    // 返回
    goBack() {
      uni.navigateBack()
    }
  },
  
  onLoad(options) {
    this.houseId = options.id
    if (this.houseId) {
      this.loadHouseInfo()
    } else {
      this.loading = false
    }
  }
}
</script>

<style scoped>
.edit-container {
  background: #f8f9fa;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.form-section {
  background: #fff;
  margin: 20rpx;
  border-radius: 20rpx;
  padding: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-row {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.form-item.half {
  flex: 1;
}

.form-item.third {
  flex: 1;
}

.label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
  font-weight: 500;
}

.input, .textarea {
  width: 100%;
  background: #f8f9fa;
  border: 2rpx solid transparent;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}

.input:focus, .textarea:focus {
  border-color: #007aff;
  background: #fff;
}

.textarea {
  height: 120rpx;
  resize: none;
}

.picker {
  background: #f8f9fa;
  border: 2rpx solid transparent;
  border-radius: 12rpx;
  padding: 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 28rpx;
  color: #333;
}

.facilities-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
}

.facility-item {
  background: #f8f9fa;
  border: 2rpx solid transparent;
  border-radius: 25rpx;
  padding: 15rpx 25rpx;
  font-size: 26rpx;
  color: #666;
}

.facility-item.active {
  background: #e3f2fd;
  border-color: #007aff;
  color: #007aff;
}

.facility-text {
  font-size: 26rpx;
}

.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(45deg, #007aff, #0056d3);
  color: #fff;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.submit-btn:disabled {
  background: #ccc;
}

.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  padding: 40rpx;
}

.loading-text, .error-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.back-btn {
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 25rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}
</style>
