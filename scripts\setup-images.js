// 图片资源快速配置脚本
// 用于快速设置图片资源，支持占位符模式

const fs = require('fs');
const path = require('path');

// 配置选项
const CONFIG_OPTIONS = {
  // 使用本地图片（需要手动下载）
  LOCAL: 'local',
  // 使用在线占位符（可立即运行）
  PLACEHOLDER: 'placeholder',
  // 混合模式（本地图片 + 占位符备用）
  HYBRID: 'hybrid'
};

// 生成配置文件
function generateImageConfig(mode = CONFIG_OPTIONS.HYBRID) {
  const configPath = path.join(__dirname, '../utils/image-config-generated.js');
  
  let configContent = '';
  
  switch (mode) {
    case CONFIG_OPTIONS.LOCAL:
      configContent = generateLocalConfig();
      break;
    case CONFIG_OPTIONS.PLACEHOLDER:
      configContent = generatePlaceholderConfig();
      break;
    case CONFIG_OPTIONS.HYBRID:
      configContent = generateHybridConfig();
      break;
    default:
      throw new Error('无效的配置模式');
  }
  
  fs.writeFileSync(configPath, configContent);
  console.log(`✅ 图片配置已生成: ${configPath}`);
  console.log(`📝 配置模式: ${mode}`);
  
  return configPath;
}

// 生成本地图片配置
function generateLocalConfig() {
  return `// 本地图片配置（自动生成）
export const IMAGE_CONFIG = {
  logo: '/static/logo.png',
  defaultAvatar: '/static/default-avatar.png',
  defaultHouse: '/static/default-house.png',
  emptyDefault: '/static/empty-default.png',
  emptyHouse: '/static/empty-house.png',
  emptyFavorite: '/static/empty-favorite.png',
  emptySearch: '/static/empty-search.png',
  banner1: '/static/banner1.jpg',
  banner2: '/static/banner2.jpg',
  banner3: '/static/banner3.jpg',
  mapMarker: '/static/map-marker.png',
  testHouse: '/static/test-house.jpg'
};

// 使用本地图片
export const getImageUrl = (imageName) => {
  return IMAGE_CONFIG[imageName] || imageName;
};
`;
}

// 生成占位符配置
function generatePlaceholderConfig() {
  return `// 占位符图片配置（自动生成）
export const IMAGE_CONFIG = {
  logo: 'https://via.placeholder.com/120x120/007aff/ffffff?text=Logo',
  defaultAvatar: 'https://via.placeholder.com/200x200/cccccc/ffffff?text=Avatar',
  defaultHouse: 'https://via.placeholder.com/400x300/f0f0f0/666666?text=House',
  emptyDefault: 'https://via.placeholder.com/200x200/f8f9fa/999999?text=Empty',
  emptyHouse: 'https://via.placeholder.com/200x200/f8f9fa/999999?text=No+House',
  emptyFavorite: 'https://via.placeholder.com/200x200/f8f9fa/999999?text=No+Favorite',
  emptySearch: 'https://via.placeholder.com/200x200/f8f9fa/999999?text=No+Result',
  banner1: 'https://via.placeholder.com/750x300/667eea/ffffff?text=Banner+1',
  banner2: 'https://via.placeholder.com/750x300/764ba2/ffffff?text=Banner+2',
  banner3: 'https://via.placeholder.com/750x300/f093fb/ffffff?text=Banner+3',
  mapMarker: 'https://via.placeholder.com/30x30/ff6b6b/ffffff?text=📍',
  testHouse: 'https://via.placeholder.com/400x300/10c560/ffffff?text=Test+House'
};

// 使用占位符图片
export const getImageUrl = (imageName) => {
  return IMAGE_CONFIG[imageName] || imageName;
};
`;
}

// 生成混合配置
function generateHybridConfig() {
  return `// 混合图片配置（自动生成）
const LOCAL_CONFIG = {
  logo: '/static/logo.png',
  defaultAvatar: '/static/default-avatar.png',
  defaultHouse: '/static/default-house.png',
  emptyDefault: '/static/empty-default.png',
  emptyHouse: '/static/empty-house.png',
  emptyFavorite: '/static/empty-favorite.png',
  emptySearch: '/static/empty-search.png',
  banner1: '/static/banner1.jpg',
  banner2: '/static/banner2.jpg',
  banner3: '/static/banner3.jpg',
  mapMarker: '/static/map-marker.png',
  testHouse: '/static/test-house.jpg'
};

const PLACEHOLDER_CONFIG = {
  logo: 'https://via.placeholder.com/120x120/007aff/ffffff?text=Logo',
  defaultAvatar: 'https://via.placeholder.com/200x200/cccccc/ffffff?text=Avatar',
  defaultHouse: 'https://via.placeholder.com/400x300/f0f0f0/666666?text=House',
  emptyDefault: 'https://via.placeholder.com/200x200/f8f9fa/999999?text=Empty',
  emptyHouse: 'https://via.placeholder.com/200x200/f8f9fa/999999?text=No+House',
  emptyFavorite: 'https://via.placeholder.com/200x200/f8f9fa/999999?text=No+Favorite',
  emptySearch: 'https://via.placeholder.com/200x200/f8f9fa/999999?text=No+Result',
  banner1: 'https://via.placeholder.com/750x300/667eea/ffffff?text=Banner+1',
  banner2: 'https://via.placeholder.com/750x300/764ba2/ffffff?text=Banner+2',
  banner3: 'https://via.placeholder.com/750x300/f093fb/ffffff?text=Banner+3',
  mapMarker: 'https://via.placeholder.com/30x30/ff6b6b/ffffff?text=📍',
  testHouse: 'https://via.placeholder.com/400x300/10c560/ffffff?text=Test+House'
};

export const IMAGE_CONFIG = LOCAL_CONFIG;

// 混合模式：优先使用本地图片，失败时回退到占位符
export const getImageUrl = (imageName) => {
  return LOCAL_CONFIG[imageName] || PLACEHOLDER_CONFIG[imageName] || imageName;
};

// 检查本地图片是否存在
export const checkLocalImage = async (imageName) => {
  return new Promise((resolve) => {
    uni.getImageInfo({
      src: LOCAL_CONFIG[imageName],
      success: () => resolve(true),
      fail: () => resolve(false)
    });
  });
};

// 获取图片URL（自动回退）
export const getImageUrlWithFallback = async (imageName) => {
  const localExists = await checkLocalImage(imageName);
  return localExists ? LOCAL_CONFIG[imageName] : PLACEHOLDER_CONFIG[imageName];
};
`;
}

// 检查静态资源目录
function checkStaticDirectory() {
  const staticDir = path.join(__dirname, '../static');
  
  if (!fs.existsSync(staticDir)) {
    fs.mkdirSync(staticDir, { recursive: true });
    console.log(`📁 已创建静态资源目录: ${staticDir}`);
  }
  
  return staticDir;
}

// 生成图片清单
function generateImageChecklist() {
  const checklistPath = path.join(__dirname, '../static/image-checklist.md');
  
  const checklist = `# 图片资源清单

## 📋 需要的图片文件

请确保以下图片文件存在于 \`static\` 目录中：

- [ ] \`logo.png\` (120x120px) - 应用Logo
- [ ] \`default-avatar.png\` (200x200px) - 默认头像
- [ ] \`default-house.png\` (400x300px) - 默认房源图片
- [ ] \`empty-default.png\` (200x200px) - 通用空状态图片
- [ ] \`empty-house.png\` (200x200px) - 无房源状态图片
- [ ] \`empty-favorite.png\` (200x200px) - 无收藏状态图片
- [ ] \`empty-search.png\` (200x200px) - 搜索无结果图片
- [ ] \`banner1.jpg\` (750x300px) - 轮播图1
- [ ] \`banner2.jpg\` (750x300px) - 轮播图2
- [ ] \`banner3.jpg\` (750x300px) - 轮播图3
- [ ] \`map-marker.png\` (30x30px) - 地图标记
- [ ] \`test-house.jpg\` (400x300px) - 测试房源图片

## 🔗 下载资源

### 快速开始（使用占位符）
\`\`\`bash
node scripts/setup-images.js placeholder
\`\`\`

### 下载真实图片
请参考 \`docs/manual-download-guide.md\` 文件。

### 检查图片完整性
\`\`\`bash
node scripts/setup-images.js check
\`\`\`
`;
  
  fs.writeFileSync(checklistPath, checklist);
  console.log(`📋 图片清单已生成: ${checklistPath}`);
}

// 检查图片文件
function checkImageFiles() {
  const staticDir = checkStaticDirectory();
  const requiredImages = [
    'logo.png',
    'default-avatar.png',
    'default-house.png',
    'empty-default.png',
    'empty-house.png',
    'empty-favorite.png',
    'empty-search.png',
    'banner1.jpg',
    'banner2.jpg',
    'banner3.jpg',
    'map-marker.png',
    'test-house.jpg'
  ];
  
  console.log('🔍 检查图片文件...');
  
  const missingFiles = [];
  const existingFiles = [];
  
  requiredImages.forEach(filename => {
    const filePath = path.join(staticDir, filename);
    if (fs.existsSync(filePath)) {
      const stats = fs.statSync(filePath);
      existingFiles.push({
        name: filename,
        size: stats.size,
        sizeKB: Math.round(stats.size / 1024)
      });
    } else {
      missingFiles.push(filename);
    }
  });
  
  console.log(`\n✅ 已存在的图片 (${existingFiles.length}/${requiredImages.length}):`);
  existingFiles.forEach(file => {
    console.log(`   ${file.name} (${file.sizeKB}KB)`);
  });
  
  if (missingFiles.length > 0) {
    console.log(`\n❌ 缺少的图片 (${missingFiles.length}):`);
    missingFiles.forEach(filename => {
      console.log(`   ${filename}`);
    });
    
    console.log(`\n💡 建议操作:`);
    console.log(`   1. 使用占位符: node scripts/setup-images.js placeholder`);
    console.log(`   2. 手动下载: 参考 docs/manual-download-guide.md`);
    console.log(`   3. 自动下载: node scripts/download-images.js download`);
  } else {
    console.log(`\n🎉 所有图片文件都已存在！`);
  }
  
  return { existingFiles, missingFiles };
}

// 主函数
function main() {
  const command = process.argv[2];
  
  switch (command) {
    case 'local':
      checkStaticDirectory();
      generateImageConfig(CONFIG_OPTIONS.LOCAL);
      generateImageChecklist();
      break;
      
    case 'placeholder':
      checkStaticDirectory();
      generateImageConfig(CONFIG_OPTIONS.PLACEHOLDER);
      console.log('🚀 已配置为占位符模式，项目可以立即运行！');
      break;
      
    case 'hybrid':
      checkStaticDirectory();
      generateImageConfig(CONFIG_OPTIONS.HYBRID);
      generateImageChecklist();
      break;
      
    case 'check':
      checkImageFiles();
      break;
      
    case 'checklist':
      generateImageChecklist();
      break;
      
    default:
      console.log(`
📖 图片资源配置脚本

用法:
  node scripts/setup-images.js <command>

命令:
  local       - 配置为本地图片模式
  placeholder - 配置为占位符模式（推荐快速开始）
  hybrid      - 配置为混合模式（本地+占位符备用）
  check       - 检查图片文件是否完整
  checklist   - 生成图片清单文件

示例:
  node scripts/setup-images.js placeholder  # 快速开始
  node scripts/setup-images.js check        # 检查文件
      `);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  generateImageConfig,
  checkImageFiles,
  generateImageChecklist,
  CONFIG_OPTIONS
};
