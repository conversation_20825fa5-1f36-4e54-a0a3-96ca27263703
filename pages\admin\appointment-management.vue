<template>
  <view class="admin-appointment-management">
    <!-- 页面标题 -->
    <uni-card title="预约管理" is-full>
      <!-- 搜索和筛选 -->
      <view class="search-section">
        <uni-row :gutter="20">
          <uni-col :span="6">
            <uni-easyinput 
              v-model="searchForm.keyword" 
              placeholder="搜索房源标题/预约人"
              @confirm="handleSearch"
            />
          </uni-col>
          <uni-col :span="4">
            <uni-data-select
              v-model="searchForm.status"
              :localdata="statusOptions"
              placeholder="预约状态"
              @change="handleSearch"
            />
          </uni-col>
          <uni-col :span="4">
            <uni-datetime-picker 
              v-model="searchForm.date"
              type="date"
              placeholder="预约日期"
              @change="handleSearch"
            />
          </uni-col>
          <uni-col :span="4">
            <button class="uni-button" type="primary" @click="handleSearch">搜索</button>
          </uni-col>
          <uni-col :span="6">
            <button class="uni-button" @click="resetSearch">重置</button>
          </uni-col>
        </uni-row>
      </view>

      <!-- 预约列表 -->
      <uni-table 
        ref="table" 
        :loading="loading" 
        border 
        stripe
      >
        <uni-tr>
          <uni-th align="center">房源标题</uni-th>
          <uni-th align="center">预约人</uni-th>
          <uni-th align="center">联系方式</uni-th>
          <uni-th align="center">预约时间</uni-th>
          <uni-th align="center">状态</uni-th>
          <uni-th align="center">创建时间</uni-th>
          <uni-th align="center">操作</uni-th>
        </uni-tr>
        <uni-tr v-for="(item, index) in appointmentList" :key="index">
          <uni-td align="center">
            <view class="house-title" @click="viewAppointment(item)">
              {{ item.house?.title || '-' }}
            </view>
          </uni-td>
          <uni-td align="center">{{ item.user?.nickname || '-' }}</uni-td>
          <uni-td align="center">{{ item.contact_phone || '-' }}</uni-td>
          <uni-td align="center">{{ formatDateTime(item.appointment_time) }}</uni-td>
          <uni-td align="center">
            <uni-tag 
              :text="getStatusText(item.status)"
              :type="getStatusType(item.status)"
            />
          </uni-td>
          <uni-td align="center">{{ formatDate(item.create_date) }}</uni-td>
          <uni-td align="center">
            <view class="action-buttons">
              <button 
                class="uni-button uni-button--mini" 
                type="primary" 
                @click="viewAppointment(item)"
              >
                查看
              </button>
              <button 
                class="uni-button uni-button--mini" 
                type="primary"
                @click="updateStatus(item, 'confirmed')"
                v-if="item.status === 'pending'"
              >
                确认
              </button>
              <button 
                class="uni-button uni-button--mini" 
                type="warn"
                @click="updateStatus(item, 'cancelled')"
                v-if="item.status === 'pending'"
              >
                取消
              </button>
            </view>
          </uni-td>
        </uni-tr>
      </uni-table>

      <!-- 分页 -->
      <view class="pagination-section">
        <uni-pagination 
          :current="pagination.page"
          :total="pagination.total"
          :page-size="pagination.pageSize"
          @change="handlePageChange"
        />
      </view>
    </uni-card>

    <!-- 预约详情弹窗 -->
    <uni-popup ref="appointmentDetailPopup" v-model="showAppointmentDetail" type="dialog">
      <uni-popup-dialog 
        title="预约详情"
        @close="showAppointmentDetail = false"
      >
        <view class="appointment-detail" v-if="currentAppointment">
          <uni-forms :model="currentAppointment" label-width="100px">
            <uni-forms-item label="房源标题">{{ currentAppointment.house?.title }}</uni-forms-item>
            <uni-forms-item label="房源地址">{{ currentAppointment.house?.location?.address }}</uni-forms-item>
            <uni-forms-item label="预约人">{{ currentAppointment.user?.nickname }}</uni-forms-item>
            <uni-forms-item label="联系电话">{{ currentAppointment.contact_phone }}</uni-forms-item>
            <uni-forms-item label="预约时间">{{ formatDateTime(currentAppointment.appointment_time) }}</uni-forms-item>
            <uni-forms-item label="备注信息">
              <view class="note">{{ currentAppointment.note || '无' }}</view>
            </uni-forms-item>
            <uni-forms-item label="状态">
              <uni-tag 
                :text="getStatusText(currentAppointment.status)"
                :type="getStatusType(currentAppointment.status)"
              />
            </uni-forms-item>
            <uni-forms-item label="创建时间">{{ formatDate(currentAppointment.create_date) }}</uni-forms-item>
            <uni-forms-item label="更新时间" v-if="currentAppointment.update_date">
              {{ formatDate(currentAppointment.update_date) }}
            </uni-forms-item>
          </uni-forms>
        </view>
      </uni-popup-dialog>
    </uni-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      loading: false,
      appointmentList: [],
      showAppointmentDetail: false,
      currentAppointment: null,
      
      // 搜索表单
      searchForm: {
        keyword: '',
        status: null,
        date: null
      },
      
      // 分页
      pagination: {
        page: 1,
        pageSize: 20,
        total: 0
      },
      
      // 选项数据
      statusOptions: [
        { value: null, text: '全部状态' },
        { value: 'pending', text: '待确认' },
        { value: 'confirmed', text: '已确认' },
        { value: 'completed', text: '已完成' },
        { value: 'cancelled', text: '已取消' }
      ]
    }
  },
  
  onLoad() {
    this.loadAppointmentList();
  },
  
  methods: {
    // 加载预约列表
    async loadAppointmentList() {
      this.loading = true;
      try {
        // 模拟数据，实际项目中应该调用云函数
        const mockData = {
          list: [
            {
              _id: '1',
              house: {
                title: '阳光小区三室一厅',
                location: { address: '北京市朝阳区阳光小区' }
              },
              user: { nickname: '张同学' },
              contact_phone: '13800138001',
              appointment_time: new Date('2024-01-15 14:00:00'),
              status: 'pending',
              note: '希望能看到房间的采光情况',
              create_date: new Date('2024-01-10 10:30:00')
            },
            {
              _id: '2',
              house: {
                title: '学府路单间出租',
                location: { address: '北京市海淀区学府路' }
              },
              user: { nickname: '李同学' },
              contact_phone: '13800138002',
              appointment_time: new Date('2024-01-16 10:00:00'),
              status: 'confirmed',
              note: '',
              create_date: new Date('2024-01-11 15:20:00')
            }
          ],
          total: 2
        };
        
        this.appointmentList = mockData.list;
        this.pagination.total = mockData.total;
        
      } catch (error) {
        console.error('加载预约列表失败:', error);
        uni.showToast({
          title: '加载失败',
          icon: 'error'
        });
      } finally {
        this.loading = false;
      }
    },
    
    // 搜索
    handleSearch() {
      this.pagination.page = 1;
      this.loadAppointmentList();
    },
    
    // 重置搜索
    resetSearch() {
      this.searchForm = {
        keyword: '',
        status: null,
        date: null
      };
      this.handleSearch();
    },
    
    // 分页变化
    handlePageChange(e) {
      this.pagination.page = e.current;
      this.loadAppointmentList();
    },
    
    // 查看预约详情
    viewAppointment(appointment) {
      this.currentAppointment = appointment;
      this.showAppointmentDetail = true;
    },
    
    // 更新预约状态
    async updateStatus(appointment, status) {
      const statusText = this.getStatusText(status);
      
      const confirmResult = await uni.showModal({
        title: '确认操作',
        content: `确定要将预约状态更改为"${statusText}"吗？`
      });
      
      if (!confirmResult.confirm) return;
      
      try {
        // 这里应该调用云函数更新状态
        uni.showToast({
          title: '状态更新成功',
          icon: 'success'
        });
        this.loadAppointmentList();
      } catch (error) {
        console.error('更新预约状态失败:', error);
        uni.showToast({
          title: '更新失败',
          icon: 'error'
        });
      }
    },
    
    // 辅助方法
    getStatusText(status) {
      const statusMap = {
        pending: '待确认',
        confirmed: '已确认',
        completed: '已完成',
        cancelled: '已取消'
      };
      return statusMap[status] || status;
    },
    
    getStatusType(status) {
      const typeMap = {
        pending: 'warning',
        confirmed: 'primary',
        completed: 'success',
        cancelled: 'error'
      };
      return typeMap[status] || 'default';
    },
    
    formatDate(date) {
      if (!date) return '-';
      return new Date(date).toLocaleDateString('zh-CN');
    },
    
    formatDateTime(date) {
      if (!date) return '-';
      return new Date(date).toLocaleString('zh-CN');
    }
  }
}
</script>

<style scoped>
.admin-appointment-management {
  padding: 20px;
}

.search-section {
  margin-bottom: 20px;
}

.house-title {
  color: #007aff;
  cursor: pointer;
  text-decoration: underline;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: wrap;
}

.pagination-section {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.appointment-detail {
  max-height: 500px;
  overflow-y: auto;
}

.note {
  max-height: 100px;
  overflow-y: auto;
  line-height: 1.5;
}
</style>
