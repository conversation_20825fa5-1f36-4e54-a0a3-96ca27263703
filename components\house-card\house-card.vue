<template>
  <view class="house-card" @click="onClick">
    <view class="house-image">
      <image 
        :src="house.images && house.images[0] || '/static/default-house.png'" 
        mode="aspectFill"
        :lazy-load="true"
      ></image>
      <view class="house-type">{{ getTypeText(house.type) }}</view>
      <view class="house-status" v-if="showStatus && house.status" :class="house.status">
        {{ getStatusText(house.status) }}
      </view>
    </view>
    <view class="house-info">
      <text class="house-title">{{ house.title }}</text>
      <text class="house-desc" v-if="showDesc">{{ house.description }}</text>
      <view class="house-tags" v-if="showTags">
        <text class="tag" v-if="house.room_count">{{ house.room_count }}室</text>
        <text class="tag" v-if="house.hall_count">{{ house.hall_count }}厅</text>
        <text class="tag" v-if="house.bathroom_count">{{ house.bathroom_count }}卫</text>
        <text class="tag" v-if="house.area">{{ house.area }}㎡</text>
        <text class="tag" v-if="house.orientation">{{ house.orientation }}</text>
      </view>
      <view class="house-location" v-if="showLocation">
        <uni-icons type="location" size="12" color="#999"></uni-icons>
        <text class="location-text">{{ house.location.district }} {{ house.location.address }}</text>
      </view>
      <view class="house-bottom">
        <view class="price-info">
          <text class="price">¥{{ house.price }}</text>
          <text class="price-unit">/月</text>
        </view>
        <view class="house-stats" v-if="showStats">
          <text class="stat-item">
            <uni-icons type="eye" size="12" color="#999"></uni-icons>
            {{ house.view_count || 0 }}
          </text>
          <text class="stat-item">
            <uni-icons type="heart" size="12" color="#999"></uni-icons>
            {{ house.favorite_count || 0 }}
          </text>
        </view>
        <text class="publish-time" v-if="showTime">{{ formatRelativeTime(house.publish_date) }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import { formatRelativeTime } from '@/utils/common.js'
import { HOUSE_TYPES, HOUSE_STATUS } from '@/common/config.js'

export default {
  name: 'HouseCard',
  props: {
    house: {
      type: Object,
      required: true
    },
    showDesc: {
      type: Boolean,
      default: true
    },
    showTags: {
      type: Boolean,
      default: true
    },
    showLocation: {
      type: Boolean,
      default: true
    },
    showStats: {
      type: Boolean,
      default: true
    },
    showTime: {
      type: Boolean,
      default: false
    },
    showStatus: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    formatRelativeTime,
    
    // 获取房源类型文本
    getTypeText(type) {
      const typeItem = HOUSE_TYPES.find(item => item.value === type)
      return typeItem ? typeItem.label : type
    },
    
    // 获取状态文本
    getStatusText(status) {
      return HOUSE_STATUS[status] || status
    },
    
    // 点击事件
    onClick() {
      this.$emit('click', this.house)
    }
  }
}
</script>

<style scoped>
.house-card {
  background: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.house-image {
  position: relative;
  height: 300rpx;
}

.house-image image {
  width: 100%;
  height: 100%;
}

.house-type {
  position: absolute;
  top: 20rpx;
  left: 20rpx;
  background: rgba(0, 122, 255, 0.9);
  color: #fff;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.house-status {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #fff;
}

.house-status.available {
  background: #10c560;
}

.house-status.rented {
  background: #ff6b6b;
}

.house-status.offline {
  background: #999;
}

.house-info {
  padding: 30rpx;
}

.house-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.house-desc {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.house-tags {
  display: flex;
  gap: 10rpx;
  margin-bottom: 20rpx;
  flex-wrap: wrap;
}

.tag {
  background: #f0f0f0;
  color: #666;
  padding: 8rpx 12rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
}

.house-location {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.location-text {
  font-size: 26rpx;
  color: #999;
  margin-left: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.house-bottom {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.price-info {
  display: flex;
  align-items: baseline;
}

.price {
  font-size: 36rpx;
  font-weight: bold;
  color: #ff6b6b;
}

.price-unit {
  font-size: 24rpx;
  color: #999;
  margin-left: 8rpx;
}

.house-stats {
  display: flex;
  gap: 20rpx;
}

.stat-item {
  font-size: 24rpx;
  color: #999;
  display: flex;
  align-items: center;
  gap: 4rpx;
}

.publish-time {
  font-size: 24rpx;
  color: #ccc;
}
</style>
