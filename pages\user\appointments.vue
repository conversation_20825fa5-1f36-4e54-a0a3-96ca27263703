<template>
  <view class="appointments-container">
    <!-- 状态筛选 -->
    <view class="status-tabs">
      <view 
        class="tab-item" 
        v-for="(tab, index) in statusTabs" 
        :key="index"
        :class="{ active: currentTab === index }"
        @click="switchTab(index)"
      >
        <text class="tab-text">{{ tab.label }}</text>
        <text class="tab-count" v-if="tab.count > 0">{{ tab.count }}</text>
      </view>
    </view>
    
    <!-- 预约列表 -->
    <scroll-view 
      class="appointment-list" 
      scroll-y 
      @scrolltolower="loadMore"
      refresher-enabled
      @refresherrefresh="onRefresh"
      :refresher-triggered="refreshing"
    >
      <view class="appointment-item" v-for="appointment in appointmentList" :key="appointment._id">
        <view class="appointment-header">
          <view class="status-badge" :class="appointment.status">
            {{ getStatusText(appointment.status) }}
          </view>
          <text class="appointment-time">{{ formatTime(appointment.create_date) }}</text>
        </view>
        
        <view class="house-info" @click="toHouseDetail(appointment.house._id)" v-if="appointment.house">
          <view class="house-image">
            <image 
              :src="appointment.house.images && appointment.house.images[0] || '/static/default-house.png'" 
              mode="aspectFill"
            ></image>
          </view>
          <view class="house-details">
            <text class="house-title">{{ appointment.house.title }}</text>
            <view class="house-tags">
              <text class="tag">{{ getTypeText(appointment.house.type) }}</text>
              <text class="tag" v-if="appointment.house.room_count">{{ appointment.house.room_count }}室</text>
              <text class="tag" v-if="appointment.house.hall_count">{{ appointment.house.hall_count }}厅</text>
            </view>
            <view class="house-location">
              <uni-icons type="location" size="12" color="#999"></uni-icons>
              <text class="location-text">{{ appointment.house.location.district }}</text>
            </view>
            <view class="house-price">
              <text class="price">¥{{ appointment.house.price }}</text>
              <text class="price-unit">/月</text>
            </view>
          </view>
        </view>
        
        <view class="appointment-details">
          <view class="detail-row">
            <text class="detail-label">预约时间:</text>
            <text class="detail-value">{{ formatDateTime(appointment.appointment_date) }}</text>
          </view>
          <view class="detail-row">
            <text class="detail-label">联系电话:</text>
            <text class="detail-value">{{ appointment.contact_phone }}</text>
          </view>
          <view class="detail-row" v-if="appointment.message">
            <text class="detail-label">备注信息:</text>
            <text class="detail-value">{{ appointment.message }}</text>
          </view>
        </view>
        
        <view class="appointment-actions" v-if="appointment.status === 'pending'">
          <button class="action-btn cancel" @click="cancelAppointment(appointment)">取消预约</button>
          <button class="action-btn contact" @click="contactLandlord(appointment.house)">联系房东</button>
        </view>
        
        <view class="appointment-actions" v-else-if="appointment.status === 'confirmed'">
          <button class="action-btn contact" @click="contactLandlord(appointment.house)">联系房东</button>
          <button class="action-btn complete" @click="completeAppointment(appointment)">完成看房</button>
        </view>
      </view>
      
      <!-- 加载状态 -->
      <view class="load-status" v-if="appointmentList.length > 0">
        <text v-if="loading">加载中...</text>
        <text v-else-if="noMore">没有更多了</text>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" v-if="!loading && appointmentList.length === 0">
        <image src="/static/empty-appointment.png" mode="aspectFit"></image>
        <text class="empty-text">暂无预约记录</text>
        <text class="empty-tip">去看看有什么好房源吧</text>
        <button class="browse-btn" @click="toBrowse">去看房</button>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import request from '@/utils/request.js'
import { formatTime, formatDateTime } from '@/utils/common.js'
import { HOUSE_TYPES, APPOINTMENT_STATUS } from '@/common/config.js'

export default {
  data() {
    return {
      currentTab: 0,
      statusTabs: [
        { label: '全部', value: '', count: 0 },
        { label: '待确认', value: 'pending', count: 0 },
        { label: '已确认', value: 'confirmed', count: 0 },
        { label: '已完成', value: 'completed', count: 0 },
        { label: '已取消', value: 'cancelled', count: 0 }
      ],
      appointmentList: [],
      loading: false,
      refreshing: false,
      noMore: false,
      page: 1,
      pageSize: 10
    }
  },
  methods: {
    formatTime,
    formatDateTime,
    
    // 获取房源类型文本
    getTypeText(type) {
      const typeItem = HOUSE_TYPES.find(item => item.value === type)
      return typeItem ? typeItem.label : type
    },
    
    // 获取状态文本
    getStatusText(status) {
      return APPOINTMENT_STATUS[status] || status
    },
    
    // 切换标签
    switchTab(index) {
      this.currentTab = index
      this.loadAppointmentList(true)
    },
    
    // 加载预约列表
    async loadAppointmentList(refresh = false) {
      if (this.loading) return
      
      this.loading = true
      
      if (refresh) {
        this.page = 1
        this.noMore = false
      }
      
      try {
        const params = {
          action: 'getAppointmentList',
          data: {
            page: this.page,
            pageSize: this.pageSize
          }
        }
        
        // 添加状态筛选
        const currentStatus = this.statusTabs[this.currentTab].value
        if (currentStatus) {
          params.data.status = currentStatus
        }
        
        const result = await request.callFunction('appointment-management', params)
        
        if (result.code === 0) {
          const { list, total } = result.data
          
          if (refresh) {
            this.appointmentList = list
          } else {
            this.appointmentList.push(...list)
          }
          
          this.page++
          this.noMore = this.appointmentList.length >= total
        }
      } catch (error) {
        console.error('加载预约列表失败:', error)
      } finally {
        this.loading = false
        this.refreshing = false
      }
    },
    
    // 加载统计数据
    async loadStatistics() {
      try {
        // 这里可以调用API获取各状态的预约数量
        // 暂时使用模拟数据
        this.statusTabs[0].count = 8
        this.statusTabs[1].count = 2
        this.statusTabs[2].count = 3
        this.statusTabs[3].count = 2
        this.statusTabs[4].count = 1
      } catch (error) {
        console.error('加载统计数据失败:', error)
      }
    },
    
    // 加载更多
    loadMore() {
      if (!this.noMore && !this.loading) {
        this.loadAppointmentList()
      }
    },
    
    // 下拉刷新
    onRefresh() {
      this.refreshing = true
      this.loadAppointmentList(true)
      this.loadStatistics()
    },
    
    // 取消预约
    async cancelAppointment(appointment) {
      uni.showModal({
        title: '提示',
        content: '确定要取消此预约吗？',
        success: async (res) => {
          if (res.confirm) {
            try {
              const result = await request.callFunction('appointment-management', {
                action: 'cancelAppointment',
                data: {
                  appointment_id: appointment._id
                }
              })
              
              if (result.code === 0) {
                uni.showToast({
                  title: '取消成功',
                  icon: 'success'
                })
                
                // 更新本地数据
                appointment.status = 'cancelled'
                
                // 重新加载统计数据
                this.loadStatistics()
              }
            } catch (error) {
              console.error('取消预约失败:', error)
            }
          }
        }
      })
    },
    
    // 完成预约
    async completeAppointment(appointment) {
      uni.showModal({
        title: '提示',
        content: '确认已完成看房吗？',
        success: async (res) => {
          if (res.confirm) {
            try {
              const result = await request.callFunction('appointment-management', {
                action: 'updateAppointment',
                data: {
                  appointment_id: appointment._id,
                  status: 'completed'
                }
              })
              
              if (result.code === 0) {
                uni.showToast({
                  title: '已完成',
                  icon: 'success'
                })
                
                // 更新本地数据
                appointment.status = 'completed'
                
                // 重新加载统计数据
                this.loadStatistics()
              }
            } catch (error) {
              console.error('更新状态失败:', error)
            }
          }
        }
      })
    },
    
    // 联系房东
    contactLandlord(house) {
      if (!house.contact) {
        uni.showToast({
          title: '暂无联系方式',
          icon: 'none'
        })
        return
      }
      
      uni.showActionSheet({
        itemList: ['拨打电话', '复制微信号'],
        success: (res) => {
          if (res.tapIndex === 0) {
            // 拨打电话
            uni.makePhoneCall({
              phoneNumber: house.contact.phone
            })
          } else if (res.tapIndex === 1) {
            // 复制微信号
            if (house.contact.wechat) {
              uni.setClipboardData({
                data: house.contact.wechat,
                success: () => {
                  uni.showToast({
                    title: '微信号已复制',
                    icon: 'success'
                  })
                }
              })
            } else {
              uni.showToast({
                title: '暂无微信号',
                icon: 'none'
              })
            }
          }
        }
      })
    },
    
    // 跳转到房源详情
    toHouseDetail(houseId) {
      uni.navigateTo({
        url: `/pages/house/detail?id=${houseId}`
      })
    },
    
    // 跳转到浏览页面
    toBrowse() {
      uni.switchTab({
        url: '/pages/house/list'
      })
    }
  },
  
  onLoad() {
    this.loadAppointmentList(true)
    this.loadStatistics()
  },
  
  onShow() {
    // 从详情页返回时刷新列表
    this.loadAppointmentList(true)
    this.loadStatistics()
  },
  
  onPullDownRefresh() {
    this.onRefresh()
    uni.stopPullDownRefresh()
  }
}
</script>

<style scoped>
.appointments-container {
  height: 100vh;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
}

.status-tabs {
  background: #fff;
  display: flex;
  border-bottom: 1rpx solid #f0f0f0;
}

.tab-item {
  flex: 1;
  padding: 30rpx 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  position: relative;
}

.tab-item.active {
  color: #007aff;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background: #007aff;
  border-radius: 2rpx;
}

.tab-text {
  font-size: 26rpx;
  color: #333;
}

.tab-item.active .tab-text {
  color: #007aff;
  font-weight: 500;
}

.tab-count {
  background: #ff4757;
  color: #fff;
  font-size: 18rpx;
  padding: 4rpx 8rpx;
  border-radius: 10rpx;
  min-width: 28rpx;
  text-align: center;
}

.appointment-list {
  flex: 1;
  padding: 20rpx;
}

.appointment-item {
  background: #fff;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.appointment-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #fff;
}

.status-badge.pending {
  background: #ff9800;
}

.status-badge.confirmed {
  background: #10c560;
}

.status-badge.completed {
  background: #007aff;
}

.status-badge.cancelled {
  background: #999;
}

.appointment-time {
  font-size: 24rpx;
  color: #999;
}

.house-info {
  display: flex;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.house-image {
  width: 120rpx;
  height: 90rpx;
  border-radius: 8rpx;
  overflow: hidden;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.house-image image {
  width: 100%;
  height: 100%;
}

.house-details {
  flex: 1;
}

.house-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.house-tags {
  display: flex;
  gap: 8rpx;
  margin-bottom: 8rpx;
}

.tag {
  background: #e0e0e0;
  color: #666;
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
  font-size: 20rpx;
}

.house-location {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.location-text {
  font-size: 22rpx;
  color: #999;
  margin-left: 6rpx;
}

.house-price {
  display: flex;
  align-items: baseline;
}

.price {
  font-size: 26rpx;
  font-weight: bold;
  color: #ff6b6b;
}

.price-unit {
  font-size: 18rpx;
  color: #999;
  margin-left: 4rpx;
}

.appointment-details {
  margin-bottom: 20rpx;
}

.detail-row {
  display: flex;
  margin-bottom: 10rpx;
}

.detail-label {
  font-size: 26rpx;
  color: #666;
  width: 160rpx;
  flex-shrink: 0;
}

.detail-value {
  font-size: 26rpx;
  color: #333;
  flex: 1;
}

.appointment-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  padding: 15rpx 20rpx;
  border-radius: 12rpx;
  font-size: 26rpx;
  border: none;
}

.action-btn.cancel {
  background: #ffebee;
  color: #f44336;
}

.action-btn.contact {
  background: #e3f2fd;
  color: #007aff;
}

.action-btn.complete {
  background: #e8f5e8;
  color: #10c560;
}

.load-status {
  text-align: center;
  padding: 40rpx;
  color: #999;
  font-size: 28rpx;
}

.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
}

.empty-state image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
}

.empty-text {
  display: block;
  font-size: 28rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.empty-tip {
  display: block;
  font-size: 24rpx;
  color: #ccc;
  margin-bottom: 40rpx;
}

.browse-btn {
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 25rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}
</style>
