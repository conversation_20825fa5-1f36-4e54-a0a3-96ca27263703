// 初始化测试数据脚本
// 在HBuilderX中运行此脚本来创建测试数据

const testUsers = [
  {
    _id: 'user_001',
    username: 'test_user_1',
    nickname: '张同学',
    avatar: '/static/default-avatar.png',
    mobile: '13800138001',
    email: '<EMAIL>',
    status: 0,
    register_date: new Date('2024-01-15'),
    last_login_date: new Date('2024-01-20'),
    create_date: new Date('2024-01-15'),
    update_date: new Date('2024-01-20')
  },
  {
    _id: 'user_002',
    username: 'test_user_2',
    nickname: '李老师',
    avatar: '/static/default-avatar.png',
    mobile: '13800138002',
    email: '<EMAIL>',
    status: 0,
    register_date: new Date('2024-01-16'),
    last_login_date: new Date('2024-01-21'),
    create_date: new Date('2024-01-16'),
    update_date: new Date('2024-01-21')
  },
  {
    _id: 'user_003',
    username: 'test_user_3',
    nickname: '王先生',
    avatar: '/static/default-avatar.png',
    mobile: '13800138003',
    email: '<EMAIL>',
    status: 0,
    register_date: new Date('2024-01-17'),
    last_login_date: new Date('2024-01-22'),
    create_date: new Date('2024-01-17'),
    update_date: new Date('2024-01-22')
  }
];

const testHouses = [
  {
    _id: 'house_001',
    title: '精装两室一厅，近地铁',
    description: '房屋位于市中心，交通便利，精装修，拎包入住。',
    type: '整租',
    area: 80,
    price: 3500,
    location: '朝阳区',
    address: '朝阳区建国路88号',
    contact_phone: '13800138001',
    images: ['/static/test-house.jpg'],
    facilities: ['空调', '洗衣机', '冰箱', '热水器', '宽带'],
    publisher_id: 'user_001',
    status: 'available',
    is_verified: true,
    view_count: 15,
    favorite_count: 3,
    publish_date: new Date('2024-01-15'),
    update_date: new Date('2024-01-15'),
    expire_date: new Date('2024-02-15')
  },
  {
    _id: 'house_002',
    title: '温馨单间，价格实惠',
    description: '单间出租，设施齐全，价格便宜，适合学生。',
    type: '合租',
    area: 25,
    price: 1200,
    location: '海淀区',
    address: '海淀区中关村大街123号',
    contact_phone: '13800138002',
    images: ['/static/test-house.jpg'],
    facilities: ['空调', '书桌', '衣柜', '宽带'],
    publisher_id: 'user_002',
    status: 'available',
    is_verified: true,
    view_count: 8,
    favorite_count: 1,
    publish_date: new Date('2024-01-16'),
    update_date: new Date('2024-01-16'),
    expire_date: new Date('2024-02-16')
  },
  {
    _id: 'house_003',
    title: '豪华三室两厅，环境优美',
    description: '高档小区，环境优美，配套设施完善。',
    type: '整租',
    area: 120,
    price: 6000,
    location: '西城区',
    address: '西城区金融街99号',
    contact_phone: '13800138003',
    images: ['/static/test-house.jpg'],
    facilities: ['空调', '洗衣机', '冰箱', '热水器', '宽带', '停车位'],
    publisher_id: 'user_003',
    status: 'available',
    is_verified: true,
    view_count: 25,
    favorite_count: 8,
    publish_date: new Date('2024-01-17'),
    update_date: new Date('2024-01-17'),
    expire_date: new Date('2024-02-17')
  }
];

const testAppointments = [
  {
    _id: 'appointment_001',
    user_id: 'user_002',
    house_id: 'house_001',
    publisher_id: 'user_001',
    appointment_date: new Date('2024-01-25 14:00:00'),
    contact_phone: '13800138002',
    message: '想看看房子，下午方便吗？',
    status: 'pending',
    create_date: new Date('2024-01-20'),
    update_date: new Date('2024-01-20')
  },
  {
    _id: 'appointment_002',
    user_id: 'user_003',
    house_id: 'house_002',
    publisher_id: 'user_002',
    appointment_date: new Date('2024-01-26 10:00:00'),
    contact_phone: '13800138003',
    message: '上午有时间看房吗？',
    status: 'confirmed',
    create_date: new Date('2024-01-21'),
    update_date: new Date('2024-01-21')
  },
  {
    _id: 'appointment_003',
    user_id: 'user_001',
    house_id: 'house_003',
    publisher_id: 'user_003',
    appointment_date: new Date('2024-01-27 16:00:00'),
    contact_phone: '13800138001',
    message: '想预约看房，谢谢！',
    status: 'completed',
    create_date: new Date('2024-01-22'),
    update_date: new Date('2024-01-22')
  }
];

// 初始化数据的函数
async function initTestData() {
  try {
    console.log('开始初始化测试数据...');
    
    // 调用云函数初始化数据
    const result = await uniCloud.callFunction({
      name: 'user-auth',
      data: {
        action: 'initTestData'
      }
    });
    
    console.log('初始化结果:', result);
    
    if (result.result && result.result.code === 0) {
      console.log('测试数据初始化成功！');
      console.log('用户数量:', result.result.data.userCount);
      console.log('房源数量:', result.result.data.houseCount);
      console.log('预约数量:', result.result.data.appointmentCount);
    } else {
      console.error('初始化失败:', result.result?.message);
    }
  } catch (error) {
    console.error('初始化测试数据失败:', error);
  }
}

// 导出数据和函数
export {
  testUsers,
  testHouses,
  testAppointments,
  initTestData
};

// 如果在浏览器环境中运行，可以直接调用
if (typeof window !== 'undefined') {
  window.initTestData = initTestData;
  console.log('测试数据初始化函数已挂载到 window.initTestData');
  console.log('可以在控制台中调用 initTestData() 来初始化数据');
}
